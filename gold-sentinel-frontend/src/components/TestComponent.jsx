import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Badge } from './ui';

const TestComponent = () => {
  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-heading font-bold text-navy-900">
        Test des composants GoldSentinel
      </h1>
      
      <Card>
        <Card.Header>
          <Card.Title>Test des boutons</Card.Title>
        </Card.Header>
        <Card.Body>
          <div className="space-x-4">
            <Button variant="primary">Primary</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="success">Success</Button>
            <Button variant="danger">Danger</Button>
          </div>
        </Card.Body>
      </Card>
      
      <Card>
        <Card.Header>
          <Card.Title>Test des badges</Card.Title>
        </Card.Header>
        <Card.Body>
          <div className="space-x-2">
            <Badge variant="success">Success</Badge>
            <Badge variant="warning">Warning</Badge>
            <Badge variant="danger">Danger</Badge>
            <Badge variant="info">Info</Badge>
            <Badge variant="gold">Gold</Badge>
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default TestComponent;
