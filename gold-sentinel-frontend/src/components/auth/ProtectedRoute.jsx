import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { tokenManager, userManager, canAccessRoute } from '../../utils/auth';
import { LoadingSpinner } from '../ui';

const ProtectedRoute = ({ children, requiredRoute = null }) => {
  const location = useLocation();
  const { loading } = useSelector((state) => state.auth);
  const [authChecked, setAuthChecked] = useState(false);
  const [isAuth, setIsAuth] = useState(false);

  useEffect(() => {
    const checkAuth = () => {
      try {
        const token = tokenManager.getToken();
        const user = userManager.getUser();
        const authenticated = !!(token && user);

        setIsAuth(authenticated);
        setAuthChecked(true);
      } catch (error) {
        console.error('Erreur lors de la vérification auth:', error);
        setIsAuth(false);
        setAuthChecked(true);
      }
    };

    checkAuth();
  }, []);

  // Afficher le spinner pendant le chargement
  if (loading || !authChecked) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="xl" />
      </div>
    );
  }

  // Vérifier si l'utilisateur est authentifié
  if (!isAuth) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Vérifier les permissions pour la route spécifique
  if (requiredRoute && !canAccessRoute(requiredRoute)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return children;
};

export default ProtectedRoute;
