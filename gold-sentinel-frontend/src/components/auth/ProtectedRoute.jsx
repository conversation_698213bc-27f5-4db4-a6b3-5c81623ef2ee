import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { isAuthenticated, canAccessRoute } from '../../utils/auth';
import { LoadingSpinner } from '../ui';

const ProtectedRoute = ({ children, requiredRoute = null }) => {
  const location = useLocation();
  const { loading } = useSelector((state) => state.auth);
  
  // Afficher le spinner pendant le chargement
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="xl" />
      </div>
    );
  }
  
  // Vérifier si l'utilisateur est authentifié
  if (!isAuthenticated()) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }
  
  // Vérifier les permissions pour la route spécifique
  if (requiredRoute && !canAccessRoute(requiredRoute)) {
    return <Navigate to="/unauthorized" replace />;
  }
  
  return children;
};

export default ProtectedRoute;
