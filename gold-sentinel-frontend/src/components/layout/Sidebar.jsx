import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { clsx } from 'clsx';
import {
  ChartBarIcon,
  EyeIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
  CurrencyDollarIcon,
  PhotoIcon,
  UsersIcon,
  CogIcon,
  DocumentTextIcon,
  MapIcon,
  BeakerIcon,
  ArrowTrendingUpIcon,
  HomeIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { userManager } from '../../utils/auth';
import { USER_NAVIGATION } from '../../constants/userTypes';
import { COMMON_NAVIGATION } from '../../constants/userNavigation';

const iconMap = {
  ChartBarIcon,
  EyeIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
  CurrencyDollarIcon,
  PhotoIcon,
  UsersIcon,
  CogIcon,
  DocumentTextIcon,
  MapIcon,
  BeakerIcon,
  ArrowTrendingUpIcon,
  HomeIcon,
  CheckCircleIcon
};

const Sidebar = ({ isOpen, onClose }) => {
  const location = useLocation();
  const userType = userManager.getUserType();
  const navigation = USER_NAVIGATION[userType] || [];

  // Navigation commune pour tous les utilisateurs
  const commonNavigation = COMMON_NAVIGATION;

  const isActiveLink = (href) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  const renderNavItem = (item) => {
    const IconComponent = iconMap[item.icon];
    const isActive = isActiveLink(item.href);

    return (
      <Link
        key={item.href}
        to={item.href}
        className={clsx(
          'nav-link',
          isActive ? 'nav-link-active' : 'nav-link-inactive'
        )}
        onClick={() => onClose && onClose()}
      >
        {IconComponent && <IconComponent className="h-5 w-5 mr-3" />}
        {item.name}
      </Link>
    );
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={clsx(
        'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0',
        isOpen ? 'translate-x-0' : '-translate-x-full'
      )}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-gradient-to-br from-gold-400 to-gold-600 rounded-lg flex items-center justify-center">
                <span className="text-navy-900 font-bold text-sm">GS</span>
              </div>
              <span className="ml-2 text-lg font-heading font-bold text-navy-900">
                GoldSentinel
              </span>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-1 overflow-y-auto">
            {/* Navigation spécifique au type d'utilisateur */}
            {navigation.length > 0 && (
              <div className="space-y-1">
                <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Mon espace
                </h3>
                {navigation.map(renderNavItem)}
              </div>
            )}

            {/* Séparateur */}
            {navigation.length > 0 && (
              <div className="border-t border-gray-200 my-6"></div>
            )}

            {/* Navigation commune */}
            <div className="space-y-1">
              <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Modules
              </h3>
              {commonNavigation.map(renderNavItem)}
            </div>
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500 text-center">
              <p>GoldSentinel v1.0</p>
              <p>© 2024 - Surveillance minière</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
