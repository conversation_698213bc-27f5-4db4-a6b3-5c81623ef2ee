import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { financialRiskService } from '../../services/api';

export const fetchFinancialRisks = createAsyncThunk(
  'financialRisk/fetchFinancialRisks',
  async (params, { rejectWithValue }) => {
    try {
      const response = await financialRiskService.getFinancialRisks(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch financial risks');
    }
  }
);

export const fetchHighImpactRisks = createAsyncThunk(
  'financialRisk/fetchHighImpactRisks',
  async (_, { rejectWithValue }) => {
    try {
      const response = await financialRiskService.getHighImpactRisks();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch high impact risks');
    }
  }
);

const initialState = {
  items: [],
  highImpactRisks: [],
  loading: false,
  error: null,
  total: 0,
  page: 1,
  pageSize: 10,
};

const financialRiskSlice = createSlice({
  name: 'financialRisk',
  initialState,
  reducers: {
    setPageSize: (state, action) => {
      state.pageSize = action.payload;
    },
    setPage: (state, action) => {
      state.page = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchFinancialRisks.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFinancialRisks.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.results || [];
        state.total = action.payload.count || 0;
      })
      .addCase(fetchFinancialRisks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(fetchHighImpactRisks.fulfilled, (state, action) => {
        state.highImpactRisks = action.payload;
      })
      .addCase(fetchHighImpactRisks.rejected, (state, action) => {
        state.error = action.payload;
      });
  },
});

export const { setPageSize, setPage } = financialRiskSlice.actions;
export default financialRiskSlice.reducer;
