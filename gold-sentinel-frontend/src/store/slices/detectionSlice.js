import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { detectionService } from '../../services/api';

export const fetchDetections = createAsyncThunk(
  'detection/fetchDetections',
  async (params, { rejectWithValue }) => {
    try {
      const response = await detectionService.getDetections(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch detections');
    }
  }
);

const initialState = {
  items: [],
  loading: false,
  error: null,
  total: 0,
  page: 1,
  pageSize: 10,
};

const detectionSlice = createSlice({
  name: 'detection',
  initialState,
  reducers: {
    setPageSize: (state, action) => {
      state.pageSize = action.payload;
    },
    setPage: (state, action) => {
      state.page = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDetections.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDetections.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.results || [];
        state.total = action.payload.count || 0;
      })
      .addCase(fetchDetections.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { setPageSize, setPage } = detectionSlice.actions;
export default detectionSlice.reducer;
