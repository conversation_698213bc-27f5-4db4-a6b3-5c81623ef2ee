import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { investigationService } from '../../services/api';

export const fetchInvestigations = createAsyncThunk(
  'investigation/fetchInvestigations',
  async (params, { rejectWithValue }) => {
    try {
      const response = await investigationService.getInvestigations(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch investigations');
    }
  }
);

export const fetchAvailableAgents = createAsyncThunk(
  'investigation/fetchAvailableAgents',
  async (_, { rejectWithValue }) => {
    try {
      const response = await investigationService.getAvailableAgents();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch available agents');
    }
  }
);

const initialState = {
  items: [],
  loading: false,
  error: null,
  total: 0,
  page: 1,
  pageSize: 10,
  availableAgents: [],
};

const investigationSlice = createSlice({
  name: 'investigation',
  initialState,
  reducers: {
    setPageSize: (state, action) => {
      state.pageSize = action.payload;
    },
    setPage: (state, action) => {
      state.page = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchInvestigations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchInvestigations.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.results || [];
        state.total = action.payload.count || 0;
      })
      .addCase(fetchInvestigations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(fetchAvailableAgents.fulfilled, (state, action) => {
        state.availableAgents = action.payload;
      })
      .addCase(fetchAvailableAgents.rejected, (state, action) => {
        state.error = action.payload;
      });
  },
});

export const { setPageSize, setPage } = investigationSlice.actions;
export default investigationSlice.reducer;
