import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { alertService } from '../../services/api';

export const fetchAlerts = createAsyncThunk(
  'alert/fetchAlerts',
  async (params, { rejectWithValue }) => {
    try {
      const response = await alertService.getAlerts(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch alerts');
    }
  }
);

const initialState = {
  items: [],
  loading: false,
  error: null,
  total: 0,
  page: 1,
  pageSize: 10,
};

const alertSlice = createSlice({
  name: 'alert',
  initialState,
  reducers: {
    setPageSize: (state, action) => {
      state.pageSize = action.payload;
    },
    setPage: (state, action) => {
      state.page = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAlerts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAlerts.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.results || [];
        state.total = action.payload.count || 0;
      })
      .addCase(fetchAlerts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { setPageSize, setPage } = alertSlice.actions;
export default alertSlice.reducer;
