import { configureStore } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import detectionReducer from './slices/detectionSlice';
import alertReducer from './slices/alertSlice';
import investigationReducer from './slices/investigationSlice';
import financialRiskReducer from './slices/financialRiskSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    detection: detectionReducer,
    alert: alertReducer,
    investigation: investigationReducer,
    financialRisk: financialRiskReducer,
  },
});
