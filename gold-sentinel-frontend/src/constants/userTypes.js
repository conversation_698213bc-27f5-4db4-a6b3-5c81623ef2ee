// Types d'utilisateurs et leurs permissions
export const USER_TYPES = {
  ADMINISTRATEUR: 'Administrateur',
  RESPONSABLE_REGIONAL: 'Responsable Régional',
  AGENT_TERRAIN: 'Agent Terrain',
  AGENT_TECHNIQUE: 'Agent Technique',
  AGENT_ANALYSTE: 'Agent Analyste'
};

// Configuration des permissions par type d'utilisateur
export const USER_PERMISSIONS = {
  [USER_TYPES.ADMINISTRATEUR]: {
    canViewAll: true,
    canManageUsers: true,
    canManageSystem: true,
    canViewLogs: true,
    canViewStats: true,
    canLaunchAnalysis: true,
    canManageInvestigations: true,
    canValidateDetections: true,
    routes: [
      '/admin/dashboard',
      '/admin/users',
      '/admin/system',
      '/admin/logs',
      '/detections',
      '/alerts',
      '/investigations',
      '/financial-risks',
      '/images',
      '/stats'
    ]
  },
  [USER_TYPES.RESPONSABLE_REGIONAL]: {
    canViewAll: false,
    canManageUsers: false,
    canManageSystem: false,
    canViewLogs: true,
    canViewStats: true,
    canLaunchAnalysis: true,
    canManageInvestigations: true,
    canValidateDetections: true,
    routes: [
      '/responsable/dashboard',
      '/responsable/detections',
      '/responsable/teams',
      '/responsable/reports',
      '/detections',
      '/alerts',
      '/investigations',
      '/financial-risks',
      '/stats'
    ]
  },
  [USER_TYPES.AGENT_TERRAIN]: {
    canViewAll: false,
    canManageUsers: false,
    canManageSystem: false,
    canViewLogs: false,
    canViewStats: false,
    canLaunchAnalysis: false,
    canManageInvestigations: false,
    canValidateDetections: false,
    routes: [
      '/terrain/dashboard',
      '/terrain/investigations',
      '/terrain/map',
      '/terrain/reports',
      '/investigations'
    ]
  },
  [USER_TYPES.AGENT_TECHNIQUE]: {
    canViewAll: false,
    canManageUsers: false,
    canManageSystem: false,
    canViewLogs: true,
    canViewStats: true,
    canLaunchAnalysis: true,
    canManageInvestigations: false,
    canValidateDetections: false,
    routes: [
      '/technique/dashboard',
      '/technique/images',
      '/technique/analysis',
      '/technique/settings',
      '/images',
      '/detections',
      '/stats'
    ]
  },
  [USER_TYPES.AGENT_ANALYSTE]: {
    canViewAll: false,
    canManageUsers: false,
    canManageSystem: false,
    canViewLogs: false,
    canViewStats: true,
    canLaunchAnalysis: false,
    canManageInvestigations: false,
    canValidateDetections: true,
    routes: [
      '/analyste/dashboard',
      '/analyste/detections',
      '/analyste/trends',
      '/analyste/reports',
      '/detections',
      '/alerts',
      '/financial-risks',
      '/stats'
    ]
  }
};

// Navigation par type d'utilisateur
export const USER_NAVIGATION = {
  [USER_TYPES.ADMINISTRATEUR]: [
    {
      name: 'Dashboard',
      href: '/admin/dashboard',
      icon: 'ChartBarIcon'
    },
    {
      name: 'Utilisateurs',
      href: '/admin/users',
      icon: 'UsersIcon'
    },
    {
      name: 'Système',
      href: '/admin/system',
      icon: 'CogIcon'
    },
    {
      name: 'Logs',
      href: '/admin/logs',
      icon: 'DocumentTextIcon'
    }
  ],
  [USER_TYPES.RESPONSABLE_REGIONAL]: [
    {
      name: 'Dashboard',
      href: '/responsable/dashboard',
      icon: 'ChartBarIcon'
    },
    {
      name: 'Détections',
      href: '/responsable/detections',
      icon: 'EyeIcon'
    },
    {
      name: 'Équipes',
      href: '/responsable/teams',
      icon: 'UsersIcon'
    },
    {
      name: 'Rapports',
      href: '/responsable/reports',
      icon: 'DocumentTextIcon'
    }
  ],
  [USER_TYPES.AGENT_TERRAIN]: [
    {
      name: 'Dashboard',
      href: '/terrain/dashboard',
      icon: 'ChartBarIcon'
    },
    {
      name: 'Investigations',
      href: '/terrain/investigations',
      icon: 'MagnifyingGlassIcon'
    },
    {
      name: 'Carte',
      href: '/terrain/map',
      icon: 'MapIcon'
    },
    {
      name: 'Rapports',
      href: '/terrain/reports',
      icon: 'DocumentTextIcon'
    }
  ],
  [USER_TYPES.AGENT_TECHNIQUE]: [
    {
      name: 'Dashboard',
      href: '/technique/dashboard',
      icon: 'ChartBarIcon'
    },
    {
      name: 'Images',
      href: '/technique/images',
      icon: 'PhotoIcon'
    },
    {
      name: 'Analyses',
      href: '/technique/analysis',
      icon: 'BeakerIcon'
    },
    {
      name: 'Paramètres',
      href: '/technique/settings',
      icon: 'CogIcon'
    }
  ],
  [USER_TYPES.AGENT_ANALYSTE]: [
    {
      name: 'Dashboard',
      href: '/analyste/dashboard',
      icon: 'ChartBarIcon'
    },
    {
      name: 'Détections',
      href: '/analyste/detections',
      icon: 'EyeIcon'
    },
    {
      name: 'Tendances',
      href: '/analyste/trends',
      icon: 'ArrowTrendingUpIcon'
    },
    {
      name: 'Rapports',
      href: '/analyste/reports',
      icon: 'DocumentTextIcon'
    }
  ]
};
