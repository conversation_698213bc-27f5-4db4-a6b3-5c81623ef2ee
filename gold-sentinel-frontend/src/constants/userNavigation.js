// Navigation spécifique par type d'utilisateur
export const USER_NAVIGATION = {
  'Administrateur': [
    {
      name: 'Tableau de bord',
      href: '/',
      icon: 'HomeIcon'
    },
    {
      name: 'Gestion Utilisateurs',
      href: '/admin/users',
      icon: 'UsersIcon'
    },
    {
      name: 'Configuration',
      href: '/admin/system',
      icon: 'CogIcon'
    },
    {
      name: 'Journaux Système',
      href: '/admin/logs',
      icon: 'DocumentTextIcon'
    },
    {
      name: 'Monitoring',
      href: '/admin/monitoring',
      icon: 'BeakerIcon'
    }
  ],

  'Responsable Régional': [
    {
      name: 'Tableau de bord',
      href: '/',
      icon: 'HomeIcon'
    },
    {
      name: 'Équipes',
      href: '/responsable/teams',
      icon: 'UsersIcon'
    },
    {
      name: 'Validations',
      href: '/responsable/validations',
      icon: 'CheckCircleIcon'
    },
    {
      name: 'Rapports Régionaux',
      href: '/responsable/reports',
      icon: 'DocumentTextIcon'
    },
    {
      name: 'Carte Régionale',
      href: '/responsable/map',
      icon: 'MapIcon'
    }
  ],

  'Agent Terrain': [
    {
      name: 'Tableau de bord',
      href: '/',
      icon: 'HomeIcon'
    },
    {
      name: 'Mes Missions',
      href: '/terrain/investigations',
      icon: 'MagnifyingGlassIcon'
    },
    {
      name: 'Carte Terrain',
      href: '/terrain/map',
      icon: 'MapIcon'
    },
    {
      name: 'Mes Rapports',
      href: '/terrain/reports',
      icon: 'DocumentTextIcon'
    },
    {
      name: 'Photos Terrain',
      href: '/terrain/photos',
      icon: 'PhotoIcon'
    }
  ],

  'Agent Technique': [
    {
      name: 'Tableau de bord',
      href: '/',
      icon: 'HomeIcon'
    },
    {
      name: 'Analyse IA',
      href: '/technique/analysis',
      icon: 'BeakerIcon'
    },
    {
      name: 'Validation Technique',
      href: '/technique/validation',
      icon: 'CheckCircleIcon'
    },
    {
      name: 'Traitement Images',
      href: '/technique/processing',
      icon: 'PhotoIcon'
    },
    {
      name: 'Paramètres IA',
      href: '/technique/settings',
      icon: 'CogIcon'
    }
  ],

  'Agent Analyste': [
    {
      name: 'Tableau de bord',
      href: '/',
      icon: 'HomeIcon'
    },
    {
      name: 'Analyse Financière',
      href: '/analyste/analysis',
      icon: 'ChartBarIcon'
    },
    {
      name: 'Modèles Prédictifs',
      href: '/analyste/models',
      icon: 'ArrowTrendingUpIcon'
    },
    {
      name: 'Rapports Analytiques',
      href: '/analyste/reports',
      icon: 'DocumentTextIcon'
    },
    {
      name: 'Tableaux de Bord',
      href: '/analyste/dashboards',
      icon: 'ChartBarIcon'
    }
  ]
};

// Navigation commune pour tous les utilisateurs (modules principaux)
export const COMMON_NAVIGATION = [
  {
    name: 'Détections',
    href: '/detections',
    icon: 'EyeIcon',
    description: 'Sites détectés par satellite'
  },
  {
    name: 'Alertes',
    href: '/alerts',
    icon: 'ExclamationTriangleIcon',
    description: 'Alertes actives et notifications'
  },
  {
    name: 'Investigations',
    href: '/investigations',
    icon: 'MagnifyingGlassIcon',
    description: 'Missions et enquêtes terrain'
  },
  {
    name: 'Risques Financiers',
    href: '/financial-risks',
    icon: 'CurrencyDollarIcon',
    description: 'Évaluation des pertes économiques'
  },
  {
    name: 'Images Satellites',
    href: '/images',
    icon: 'PhotoIcon',
    description: 'Imagerie satellite et analyse'
  },
  {
    name: 'Statistiques',
    href: '/stats',
    icon: 'ChartBarIcon',
    description: 'Tableaux de bord et métriques'
  }
];

// Permissions d'accès par type d'utilisateur
export const USER_ACCESS_PERMISSIONS = {
  'Administrateur': {
    canViewAll: true,
    canManageUsers: true,
    canManageSystem: true,
    canViewLogs: true,
    canExportData: true,
    modules: ['detections', 'alerts', 'investigations', 'financial-risks', 'images', 'stats', 'admin']
  },

  'Responsable Régional': {
    canViewAll: false,
    canManageUsers: false,
    canManageSystem: false,
    canViewLogs: false,
    canExportData: true,
    canValidateDetections: true,
    canAssignMissions: true,
    canManageTeam: true,
    modules: ['detections', 'alerts', 'investigations', 'stats', 'responsable']
  },

  'Agent Terrain': {
    canViewAll: false,
    canManageUsers: false,
    canManageSystem: false,
    canViewLogs: false,
    canExportData: false,
    canSubmitReports: true,
    canTakePhotos: true,
    canViewAssignedMissions: true,
    modules: ['terrain']
  },

  'Agent Technique': {
    canViewAll: false,
    canManageUsers: false,
    canManageSystem: false,
    canViewLogs: false,
    canExportData: true,
    canProcessImages: true,
    canValidateTechnical: true,
    canConfigureAI: true,
    modules: ['detections', 'images', 'technique']
  },

  'Agent Analyste': {
    canViewAll: false,
    canManageUsers: false,
    canManageSystem: false,
    canViewLogs: false,
    canExportData: true,
    canCreateReports: true,
    canViewFinancialData: true,
    canCreateModels: true,
    modules: ['detections', 'financial-risks', 'stats', 'analyste']
  }
};

// Fonction utilitaire pour vérifier les permissions
export const hasPermission = (userType, permission) => {
  const permissions = USER_ACCESS_PERMISSIONS[userType];
  return permissions ? permissions[permission] || false : false;
};

// Fonction utilitaire pour vérifier l'accès à un module
export const canAccessModule = (userType, module) => {
  const permissions = USER_ACCESS_PERMISSIONS[userType];
  return permissions ? permissions.modules.includes(module) : false;
};
