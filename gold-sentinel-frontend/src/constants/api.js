// Configuration API
export const API_BASE_URL = 'http://localhost:8000/api/v1';

// Endpoints API
export const API_ENDPOINTS = {
  // Authentification
  AUTH: {
    LOGIN: '/auth/token/',
    REFRESH: '/auth/token/refresh/',
    LOGOUT: '/auth/logout/'
  },

  // Analyses
  ANALYSIS: {
    BASE: '/analysis',
    RUN: '/analysis/run/'
  },

  // Images satellites
  IMAGES: {
    BASE: '/images',
    UPLOAD: '/images/upload/',
    PROCESS: '/images/process/'
  },

  // Détections
  DETECTIONS: {
    BASE: '/detections',
    VALIDATE: '/detections/{id}/validate/',
    INVALIDATE: '/detections/{id}/invalidate/'
  },

  // Alertes
  ALERTS: {
    BASE: '/alerts',
    MARK_READ: '/alerts/{id}/mark-read/',
    ASSIGN: '/alerts/{id}/assign/'
  },

  // Risques financiers
  FINANCIAL_RISKS: {
    BASE: '/financial-risks',
    HIGH_IMPACT: '/financial-risks/high-impact/'
  },

  // Investigations
  INVESTIGATIONS: {
    BASE: '/investigations',
    ASSIGN: '/investigations/{id}/assign/',
    COMPLETE: '/investigations/{id}/complete/',
    AVAILABLE_AGENTS: '/investigations/available-agents/'
  },

  // Statistiques
  STATS: {
    DASHBOARD: '/stats/dashboard/',
    DETECTION_TRENDS: '/stats/detection-trends/',
    REGIONAL_STATS: '/stats/regional/',
    USER_ACTIVITY: '/stats/user-activity/'
  },

  // Feedbacks
  FEEDBACKS: {
    BASE: '/feedbacks'
  },

  // Événements
  EVENTS: {
    BASE: '/events'
  }
};

// Status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
};

// Messages d'erreur
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Erreur de connexion réseau',
  UNAUTHORIZED: 'Accès non autorisé',
  FORBIDDEN: 'Accès interdit',
  NOT_FOUND: 'Ressource non trouvée',
  SERVER_ERROR: 'Erreur serveur interne',
  VALIDATION_ERROR: 'Erreur de validation des données'
};
