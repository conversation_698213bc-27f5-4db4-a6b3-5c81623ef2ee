import React from 'react';
import { userManager } from '../utils/auth';
import AdminDashboard from './admin/AdminDashboard';
import ResponsableDashboard from './responsable/ResponsableDashboard';
import TerrainDashboard from './terrain/TerrainDashboard';

const Dashboard = () => {
  const userType = userManager.getUserType();

  // Redirection vers le dashboard spécialisé selon le type d'utilisateur
  const renderDashboard = () => {
    switch (userType) {
      case 'Administrateur':
        return <AdminDashboard />;
      case 'Responsable Régional':
        return <ResponsableDashboard />;
      case 'Agent Terrain':
        return <TerrainDashboard />;
      case 'Agent Technique':
        // TODO: Créer TechniqueDashboard
        return <div>Dashboard Agent Technique (à implémenter)</div>;
      case 'Agent Analyste':
        // TODO: Créer AnalysteDashboard
        return <div>Dashboard Agent Analyste (à implémenter)</div>;
      default:
        // Dashboard par défaut pour les utilisateurs non reconnus
        return <AdminDashboard />;
    }
  };

  return renderDashboard();
};

export default Dashboard;
