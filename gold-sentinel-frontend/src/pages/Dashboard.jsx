import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  ChartBarIcon,
  EyeIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
  CurrencyDollarIcon,
  PhotoIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import { Card, Badge, StatusIndicator } from '../components/ui';
import { userManager } from '../utils/auth';
import { formatNumber, formatCurrency, formatTimeAgo } from '../utils/formatters';

const Dashboard = () => {
  const [stats, setStats] = useState({
    detections: 0,
    alerts: 0,
    investigations: 0,
    risks: 0,
    images: 0,
    totalLoss: 0
  });

  const [recentActivity, setRecentActivity] = useState([]);
  const [systemStatus, setSystemStatus] = useState([]);

  const userType = userManager.getUserType();
  const currentUser = userManager.getUser();

  useEffect(() => {
    // Mock data - À remplacer par des appels API réels
    setStats({
      detections: 127,
      alerts: 23,
      investigations: 45,
      risks: 8,
      images: 1250,
      totalLoss: 2500000
    });

    setRecentActivity([
      {
        id: 1,
        type: 'detection',
        message: 'Nouvelle détection de site minier',
        time: new Date(Date.now() - 2 * 60 * 60 * 1000),
        severity: 'warning'
      },
      {
        id: 2,
        type: 'alert',
        message: 'Alerte critique - Zone protégée',
        time: new Date(Date.now() - 4 * 60 * 60 * 1000),
        severity: 'error'
      },
      {
        id: 3,
        type: 'investigation',
        message: 'Investigation terrain terminée',
        time: new Date(Date.now() - 24 * 60 * 60 * 1000),
        severity: 'success'
      },
      {
        id: 4,
        type: 'analysis',
        message: 'Analyse satellite complétée',
        time: new Date(Date.now() - 48 * 60 * 60 * 1000),
        severity: 'info'
      }
    ]);

    setSystemStatus([
      {
        name: 'Analyse satellite',
        status: 'active',
        description: 'Traitement en cours'
      },
      {
        name: 'Détection automatique',
        status: 'active',
        description: 'Fonctionnel'
      },
      {
        name: 'Système d\'alerte',
        status: 'warning',
        description: 'Maintenance programmée'
      },
      {
        name: 'Base de données',
        status: 'active',
        description: 'Opérationnelle'
      }
    ]);
  }, []);

  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    let greeting = 'Bonjour';
    if (hour >= 18) greeting = 'Bonsoir';
    else if (hour >= 12) greeting = 'Bon après-midi';

    return `${greeting}, ${currentUser?.first_name || 'Utilisateur'}`;
  };

  const cards = [
    {
      title: 'Détections',
      value: formatNumber(stats.detections),
      icon: EyeIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      link: '/detections',
      description: 'Sites détectés'
    },
    {
      title: 'Alertes',
      value: formatNumber(stats.alerts),
      icon: ExclamationTriangleIcon,
      color: 'text-alert-600',
      bgColor: 'bg-alert-50',
      link: '/alerts',
      description: 'Alertes actives'
    },
    {
      title: 'Investigations',
      value: formatNumber(stats.investigations),
      icon: MagnifyingGlassIcon,
      color: 'text-forest-600',
      bgColor: 'bg-forest-50',
      link: '/investigations',
      description: 'En cours'
    },
    {
      title: 'Risques Financiers',
      value: formatCurrency(stats.totalLoss),
      icon: CurrencyDollarIcon,
      color: 'text-warning-600',
      bgColor: 'bg-warning-50',
      link: '/financial-risks',
      description: 'Pertes estimées'
    }
  ];

  const getActivityIcon = (type) => {
    switch (type) {
      case 'detection': return EyeIcon;
      case 'alert': return ExclamationTriangleIcon;
      case 'investigation': return MagnifyingGlassIcon;
      case 'analysis': return ChartBarIcon;
      default: return ClockIcon;
    }
  };

  const getActivityBadgeVariant = (severity) => {
    switch (severity) {
      case 'error': return 'danger';
      case 'warning': return 'warning';
      case 'success': return 'success';
      case 'info': return 'info';
      default: return 'info';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-navy-900 to-navy-800 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-heading font-bold">
              {getWelcomeMessage()}
            </h1>
            <p className="text-navy-200 mt-1">
              {userType} - Tableau de bord GoldSentinel
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-navy-200">Dernière mise à jour</p>
            <p className="text-gold-400 font-medium">
              {new Date().toLocaleString('fr-FR')}
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {cards.map((card) => {
          const Icon = card.icon;
          return (
            <Link key={card.title} to={card.link}>
              <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
                <Card.Body className="p-6">
                  <div className="flex items-center">
                    <div className={`${card.bgColor} p-3 rounded-lg`}>
                      <Icon className={`h-6 w-6 ${card.color}`} />
                    </div>
                    <div className="ml-4 flex-1">
                      <p className="text-sm font-medium text-gray-600">
                        {card.title}
                      </p>
                      <p className="text-2xl font-bold text-navy-900">
                        {card.value}
                      </p>
                      <p className="text-xs text-gray-500">
                        {card.description}
                      </p>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Link>
          );
        })}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card>
          <Card.Header>
            <Card.Title>Activité récente</Card.Title>
            <Card.Description>
              Derniers événements du système
            </Card.Description>
          </Card.Header>
          <Card.Body>
            <div className="space-y-4">
              {recentActivity.map((activity) => {
                const Icon = getActivityIcon(activity.type);
                return (
                  <div key={activity.id} className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <Icon className="h-5 w-5 text-gray-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">
                        {activity.message}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatTimeAgo(activity.time)}
                      </p>
                    </div>
                    <Badge variant={getActivityBadgeVariant(activity.severity)}>
                      {activity.severity}
                    </Badge>
                  </div>
                );
              })}
            </div>
          </Card.Body>
        </Card>

        {/* System Status */}
        <Card>
          <Card.Header>
            <Card.Title>État du système</Card.Title>
            <Card.Description>
              Statut des services GoldSentinel
            </Card.Description>
          </Card.Header>
          <Card.Body>
            <div className="space-y-4">
              {systemStatus.map((service, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <StatusIndicator status={service.status} showLabel={false} />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {service.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {service.description}
                      </p>
                    </div>
                  </div>
                  <Badge
                    variant={service.status === 'active' ? 'success' : 'warning'}
                  >
                    {service.status === 'active' ? 'Actif' : 'Attention'}
                  </Badge>
                </div>
              ))}
            </div>
          </Card.Body>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
