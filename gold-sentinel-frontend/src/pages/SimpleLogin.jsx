import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Button, Input } from '../components/ui';
import { authService } from '../services/api';
import { userManager, tokenManager } from '../utils/auth';
import toast from 'react-hot-toast';

const SimpleLogin = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [credentials, setCredentials] = useState({
    email: '<EMAIL>',
    password: 'admin123'
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      console.log('Tentative de connexion avec:', credentials);
      
      const response = await authService.login(credentials);
      console.log('Réponse de connexion:', response.data);
      
      const { access, refresh, user } = response.data;
      
      // Sauvegarder les données
      tokenManager.setToken(access);
      tokenManager.setRefreshToken(refresh);
      userManager.setUser(user);
      
      toast.success('Connexion réussie !');
      
      // Rediriger vers le dashboard
      navigate('/', { replace: true });
      
    } catch (error) {
      console.error('Erreur de connexion:', error);
      toast.error(`Erreur: ${error.response?.data?.detail || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testUsers = [
    { email: '<EMAIL>', password: 'admin123', name: 'Admin' },
    { email: '<EMAIL>', password: 'password123', name: 'Responsable' },
    { email: '<EMAIL>', password: 'password123', name: 'Agent 1' },
    { email: '<EMAIL>', password: 'password123', name: 'Agent 2' }
  ];

  const quickLogin = (user) => {
    setCredentials({ email: user.email, password: user.password });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-navy-900 via-navy-800 to-navy-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="h-16 w-16 bg-gradient-to-br from-gold-400 to-gold-600 rounded-xl flex items-center justify-center shadow-gold mx-auto mb-4">
            <span className="text-navy-900 font-bold text-2xl">GS</span>
          </div>
          <h1 className="text-3xl font-heading font-bold text-white">
            GoldSentinel
          </h1>
          <p className="text-gray-300 mt-2">Connexion Simple</p>
        </div>

        {/* Formulaire de connexion */}
        <Card>
          <Card.Body className="p-6">
            <form onSubmit={handleSubmit} className="space-y-4">
              <Input
                type="email"
                label="Email"
                value={credentials.email}
                onChange={(e) => setCredentials(prev => ({ ...prev, email: e.target.value }))}
                required
              />
              
              <Input
                type="password"
                label="Mot de passe"
                value={credentials.password}
                onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                required
              />
              
              <Button 
                type="submit" 
                variant="primary" 
                className="w-full"
                loading={loading}
              >
                Se connecter
              </Button>
            </form>
          </Card.Body>
        </Card>

        {/* Connexions rapides */}
        <div className="mt-6">
          <Card className="bg-navy-800 border-navy-600">
            <Card.Body className="p-4">
              <h3 className="text-white font-semibold mb-3 text-center">
                Connexions rapides
              </h3>
              <div className="grid grid-cols-2 gap-2">
                {testUsers.map((user, index) => (
                  <Button
                    key={index}
                    onClick={() => quickLogin(user)}
                    variant="outline"
                    size="sm"
                    className="text-white border-white hover:bg-white hover:text-navy-900"
                  >
                    {user.name}
                  </Button>
                ))}
              </div>
            </Card.Body>
          </Card>
        </div>

        {/* Info */}
        <div className="mt-4 text-center">
          <p className="text-gray-400 text-sm">
            Backend Django : http://localhost:8001
          </p>
        </div>
      </div>
    </div>
  );
};

export default SimpleLogin;
