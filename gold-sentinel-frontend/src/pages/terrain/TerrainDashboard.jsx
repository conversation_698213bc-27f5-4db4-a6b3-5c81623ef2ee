import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  MapIcon,
  MagnifyingGlassIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CameraIcon,
  DocumentTextIcon,
  NavigationIcon
} from '@heroicons/react/24/outline';
import { Card, Badge, StatusIndicator, Button } from '../../components/ui';
import { useApi } from '../../hooks/useApi';
import { investigationService } from '../../services/api';
import { formatTimeAgo, formatCoordinates } from '../../utils/formatters';

const TerrainDashboard = () => {
  const { data: investigations, loading } = useApi(
    () => investigationService.getInvestigations({ assigned_to_me: true }),
    { immediate: true }
  );

  const missionStats = [
    {
      title: 'Missions Assignées',
      value: investigations?.count || 5,
      icon: MagnifyingGlassIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'En Cours',
      value: investigations?.results?.filter(i => i.status === 'IN_PROGRESS').length || 2,
      icon: ClockIcon,
      color: 'text-warning-600',
      bgColor: 'bg-warning-50'
    },
    {
      title: 'Terminées',
      value: investigations?.results?.filter(i => i.status === 'COMPLETED').length || 8,
      icon: CheckCircleIcon,
      color: 'text-success-600',
      bgColor: 'bg-success-50'
    },
    {
      title: 'Urgentes',
      value: investigations?.results?.filter(i => i.priority === 'HIGH').length || 1,
      icon: ExclamationTriangleIcon,
      color: 'text-alert-600',
      bgColor: 'bg-alert-50'
    }
  ];

  const activeMissions = [
    {
      id: 1,
      title: 'Investigation site minier - Tanda',
      priority: 'high',
      status: 'IN_PROGRESS',
      assignedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      deadline: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000),
      location: { lat: 8.1667, lon: -3.4833 },
      description: 'Vérifier les activités suspectes signalées par satellite',
      estimatedDuration: '4 heures'
    },
    {
      id: 2,
      title: 'Contrôle zone forestière - Bondoukou',
      priority: 'medium',
      status: 'ASSIGNED',
      assignedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      location: { lat: 8.0333, lon: -2.8000 },
      description: 'Inspection de routine de la zone protégée',
      estimatedDuration: '6 heures'
    }
  ];

  const recentReports = [
    {
      id: 1,
      title: 'Rapport site minier illégal',
      submittedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      status: 'validated',
      location: 'Bouna',
      findings: 'Site confirmé, activité en cours'
    },
    {
      id: 2,
      title: 'Inspection zone tampon',
      submittedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      status: 'pending',
      location: 'Tanda',
      findings: 'Aucune activité suspecte détectée'
    }
  ];

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'danger';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'info';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'IN_PROGRESS': return 'warning';
      case 'ASSIGNED': return 'info';
      case 'PENDING': return 'warning';
      default: return 'info';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'COMPLETED': return 'Terminée';
      case 'IN_PROGRESS': return 'En cours';
      case 'ASSIGNED': return 'Assignée';
      case 'PENDING': return 'En attente';
      case 'validated': return 'Validé';
      case 'pending': return 'En attente';
      default: return status;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-forest-800 to-forest-700 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-heading font-bold">
              Missions Terrain
            </h1>
            <p className="text-forest-200 mt-1">
              Agent Terrain - Investigations et contrôles sur site
            </p>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-2">
              <NavigationIcon className="h-5 w-5 text-forest-200" />
              <span className="text-forest-200">Position GPS active</span>
            </div>
            <p className="text-gold-400 font-medium mt-1">
              {new Date().toLocaleString('fr-FR')}
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {missionStats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title} className="hover:shadow-lg transition-shadow duration-200">
              <Card.Body className="p-6">
                <div className="flex items-center">
                  <div className={`${stat.bgColor} p-3 rounded-lg`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className="ml-4 flex-1">
                    <p className="text-sm font-medium text-gray-600">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-navy-900">
                      {stat.value}
                    </p>
                  </div>
                </div>
              </Card.Body>
            </Card>
          );
        })}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Missions */}
        <Card>
          <Card.Header>
            <Card.Title>Missions Actives</Card.Title>
            <Card.Description>
              Investigations en cours et assignées
            </Card.Description>
          </Card.Header>
          <Card.Body>
            <div className="space-y-4">
              {activeMissions.map((mission) => (
                <div key={mission.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="text-sm font-medium text-gray-900">
                          {mission.title}
                        </h4>
                        <Badge variant={getPriorityColor(mission.priority)} size="sm">
                          {mission.priority}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-600 mb-2">
                        {mission.description}
                      </p>
                      <div className="grid grid-cols-2 gap-2 text-xs text-gray-500">
                        <div>
                          <span className="font-medium">Localisation:</span><br />
                          {formatCoordinates(mission.location.lat, mission.location.lon)}
                        </div>
                        <div>
                          <span className="font-medium">Échéance:</span><br />
                          {formatTimeAgo(mission.deadline)}
                        </div>
                      </div>
                    </div>
                    <Badge variant={getStatusColor(mission.status)} size="sm">
                      {getStatusLabel(mission.status)}
                    </Badge>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="primary" size="sm" className="flex-1">
                      <MapIcon className="h-4 w-4 mr-1" />
                      Voir sur carte
                    </Button>
                    <Button variant="outline" size="sm">
                      <DocumentTextIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </Card.Body>
          <Card.Footer>
            <Link to="/terrain/investigations">
              <Button variant="outline" size="sm" className="w-full">
                Voir toutes les missions
              </Button>
            </Link>
          </Card.Footer>
        </Card>

        {/* Recent Reports */}
        <Card>
          <Card.Header>
            <Card.Title>Rapports Récents</Card.Title>
            <Card.Description>
              Derniers rapports soumis
            </Card.Description>
          </Card.Header>
          <Card.Body>
            <div className="space-y-4">
              {recentReports.map((report) => (
                <div key={report.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-900">
                      {report.title}
                    </h4>
                    <Badge variant={getStatusColor(report.status)} size="sm">
                      {getStatusLabel(report.status)}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">
                    {report.findings}
                  </p>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{report.location}</span>
                    <span>{formatTimeAgo(report.submittedAt)}</span>
                  </div>
                </div>
              ))}
            </div>
          </Card.Body>
          <Card.Footer>
            <Link to="/terrain/reports">
              <Button variant="outline" size="sm" className="w-full">
                Voir tous les rapports
              </Button>
            </Link>
          </Card.Footer>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Link to="/terrain/map">
          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            <Card.Body className="p-6 text-center">
              <MapIcon className="h-8 w-8 text-blue-600 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900">Carte</h3>
              <p className="text-sm text-gray-500 mt-1">
                Navigation et localisation
              </p>
            </Card.Body>
          </Card>
        </Link>

        <Link to="/terrain/investigations">
          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            <Card.Body className="p-6 text-center">
              <MagnifyingGlassIcon className="h-8 w-8 text-green-600 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900">Investigations</h3>
              <p className="text-sm text-gray-500 mt-1">
                Missions assignées
              </p>
            </Card.Body>
          </Card>
        </Link>

        <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
          <Card.Body className="p-6 text-center">
            <CameraIcon className="h-8 w-8 text-purple-600 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-gray-900">Photos</h3>
            <p className="text-sm text-gray-500 mt-1">
              Capturer des preuves
            </p>
          </Card.Body>
        </Card>

        <Link to="/terrain/reports">
          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            <Card.Body className="p-6 text-center">
              <DocumentTextIcon className="h-8 w-8 text-orange-600 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900">Rapports</h3>
              <p className="text-sm text-gray-500 mt-1">
                Soumettre un rapport
              </p>
            </Card.Body>
          </Card>
        </Link>
      </div>
    </div>
  );
};

export default TerrainDashboard;
