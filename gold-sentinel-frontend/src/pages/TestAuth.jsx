import React from 'react';
import { <PERSON>, But<PERSON> } from '../components/ui';
import { useAuth } from '../hooks/useAuth';
import { userManager, tokenManager } from '../utils/auth';

const TestAuth = () => {
  const { user, handleLogout } = useAuth();

  const testLogin = () => {
    // Simuler une connexion pour test
    const testUser = {
      id: 1,
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
      primary_authority: 'Administrateur',
      authorities: ['Administrateur']
    };
    
    userManager.setUser(testUser);
    tokenManager.setToken('test-token-123');
    
    // Recharger la page pour appliquer les changements
    window.location.reload();
  };

  return (
    <div className="p-6">
      <Card>
        <Card.Header>
          <h1 className="text-2xl font-bold">Test d'Authentification</h1>
        </Card.Header>
        <Card.Body className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">État de l'authentification :</h3>
            <p>Utilisateur connecté : {user ? 'Oui' : 'Non'}</p>
            {user && (
              <div className="mt-2 p-3 bg-gray-100 rounded">
                <p><strong>Email :</strong> {user.email}</p>
                <p><strong>Nom :</strong> {user.first_name} {user.last_name}</p>
                <p><strong>Rôle :</strong> {user.primary_authority}</p>
              </div>
            )}
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">Tokens :</h3>
            <p>Token : {tokenManager.getToken() ? 'Présent' : 'Absent'}</p>
            <p>Refresh Token : {tokenManager.getRefreshToken() ? 'Présent' : 'Absent'}</p>
          </div>
          
          <div className="flex space-x-2">
            {!user ? (
              <Button onClick={testLogin} variant="primary">
                Connexion Test
              </Button>
            ) : (
              <Button onClick={handleLogout} variant="danger">
                Déconnexion
              </Button>
            )}
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default TestAuth;
