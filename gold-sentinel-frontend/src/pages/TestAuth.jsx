import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { Card, Button, Badge } from '../components/ui';
import { userManager } from '../utils/auth';
import { USER_ACCESS_PERMISSIONS } from '../constants/userNavigation';

const TestAuth = () => {
  const { user, userType, userAuthorities, isAuthenticated, logout } = useAuth();
  const [testResults, setTestResults] = useState([]);

  const runTests = () => {
    const results = [];

    // Test 1: Vérification de l'authentification
    results.push({
      name: 'Authentification',
      status: isAuthenticated ? 'success' : 'error',
      message: isAuthenticated ? 'Utilisateur authentifié' : 'Utilisateur non authentifié'
    });

    // Test 2: Vérification des données utilisateur
    results.push({
      name: 'Données utilisateur',
      status: user ? 'success' : 'error',
      message: user ? `Utilisateur: ${user.first_name} ${user.last_name}` : 'Aucune donnée utilisateur'
    });

    // Test 3: Vérification du type d'utilisateur
    results.push({
      name: 'Type d\'utilisateur',
      status: userType ? 'success' : 'error',
      message: userType ? `Type: ${userType}` : 'Type d\'utilisateur non défini'
    });

    // Test 4: Vérification des autorités
    results.push({
      name: 'Autorités',
      status: userAuthorities.length > 0 ? 'success' : 'warning',
      message: `Autorités: ${userAuthorities.join(', ')}`
    });

    // Test 5: Vérification des permissions
    const permissions = USER_ACCESS_PERMISSIONS[userType];
    results.push({
      name: 'Permissions',
      status: permissions ? 'success' : 'error',
      message: permissions ? `${Object.keys(permissions).length} permissions définies` : 'Aucune permission'
    });

    setTestResults(results);
  };

  const simulateUserTypes = [
    'Administrateur',
    'Responsable Régional',
    'Agent Terrain',
    'Agent Technique',
    'Agent Analyste'
  ];

  const simulateUserType = (newUserType) => {
    // Simulation temporaire du changement de type d'utilisateur
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
      primary_authority: newUserType,
      authorities: [newUserType],
      is_active: true
    };

    // Sauvegarder temporairement
    localStorage.setItem('user', JSON.stringify(mockUser));
    
    // Recharger la page pour appliquer les changements
    window.location.reload();
  };

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-6 text-white">
        <h1 className="text-2xl font-heading font-bold">
          Test d'Authentification GoldSentinel
        </h1>
        <p className="text-blue-200 mt-1">
          Vérification du système d'authentification et des permissions
        </p>
      </div>

      {/* Informations utilisateur actuelles */}
      <Card>
        <Card.Header>
          <Card.Title>Informations Utilisateur Actuelles</Card.Title>
        </Card.Header>
        <Card.Body>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-900">Authentifié</h4>
              <Badge variant={isAuthenticated ? 'success' : 'danger'}>
                {isAuthenticated ? 'Oui' : 'Non'}
              </Badge>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900">Type d'utilisateur</h4>
              <Badge variant="info">{userType || 'Non défini'}</Badge>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900">Nom complet</h4>
              <p className="text-gray-600">
                {user ? `${user.first_name} ${user.last_name}` : 'Non disponible'}
              </p>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900">Email</h4>
              <p className="text-gray-600">{user?.email || 'Non disponible'}</p>
            </div>
            
            <div className="md:col-span-2">
              <h4 className="font-medium text-gray-900">Autorités</h4>
              <div className="flex flex-wrap gap-2 mt-1">
                {userAuthorities.map((authority, index) => (
                  <Badge key={index} variant="outline">
                    {authority}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Tests automatiques */}
      <Card>
        <Card.Header>
          <Card.Title>Tests Automatiques</Card.Title>
          <Card.Description>
            Vérification des fonctionnalités d'authentification
          </Card.Description>
        </Card.Header>
        <Card.Body>
          <div className="space-y-4">
            <Button onClick={runTests} variant="primary">
              Exécuter les tests
            </Button>
            
            {testResults.length > 0 && (
              <div className="space-y-3">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">{result.name}</h4>
                      <p className="text-sm text-gray-600">{result.message}</p>
                    </div>
                    <Badge variant={result.status}>
                      {result.status === 'success' ? 'Réussi' : 
                       result.status === 'warning' ? 'Attention' : 'Échec'}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </div>
        </Card.Body>
      </Card>

      {/* Simulation des types d'utilisateur */}
      <Card>
        <Card.Header>
          <Card.Title>Simulation des Types d'Utilisateur</Card.Title>
          <Card.Description>
            Tester l'interface avec différents types d'utilisateur
          </Card.Description>
        </Card.Header>
        <Card.Body>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {simulateUserTypes.map((type) => (
              <Button
                key={type}
                variant={userType === type ? 'primary' : 'outline'}
                onClick={() => simulateUserType(type)}
                className="text-left"
              >
                <div>
                  <div className="font-medium">{type}</div>
                  <div className="text-xs opacity-75">
                    {userType === type ? 'Actuel' : 'Simuler'}
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </Card.Body>
      </Card>

      {/* Permissions actuelles */}
      <Card>
        <Card.Header>
          <Card.Title>Permissions Actuelles</Card.Title>
          <Card.Description>
            Permissions accordées au type d'utilisateur actuel
          </Card.Description>
        </Card.Header>
        <Card.Body>
          {USER_ACCESS_PERMISSIONS[userType] ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(USER_ACCESS_PERMISSIONS[userType]).map(([permission, value]) => (
                <div key={permission} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900">
                    {permission.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </span>
                  <Badge variant={value === true ? 'success' : value === false ? 'danger' : 'info'}>
                    {Array.isArray(value) ? `${value.length} modules` : 
                     typeof value === 'boolean' ? (value ? 'Oui' : 'Non') : 
                     value.toString()}
                  </Badge>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">Aucune permission définie pour ce type d'utilisateur</p>
          )}
        </Card.Body>
      </Card>

      {/* Actions */}
      <Card>
        <Card.Header>
          <Card.Title>Actions</Card.Title>
        </Card.Header>
        <Card.Body>
          <div className="flex space-x-4">
            <Button variant="danger" onClick={logout}>
              Se déconnecter
            </Button>
            <Button variant="outline" onClick={() => window.location.reload()}>
              Recharger la page
            </Button>
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default TestAuth;
