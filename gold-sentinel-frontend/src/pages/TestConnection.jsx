import React, { useState } from 'react';
import { <PERSON>, Button } from '../components/ui';
import { authService } from '../services/api';
import { userManager, tokenManager } from '../utils/auth';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';

const TestConnection = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'admin123',
      name: 'Admin System',
      role: 'Administrateur'
    },
    {
      email: '<EMAIL>',
      password: 'password123',
      name: '<PERSON>',
      role: 'Responsable Régional'
    },
    {
      email: '<EMAIL>',
      password: 'password123',
      name: '<PERSON>',
      role: 'Agent Terra<PERSON>'
    },
    {
      email: '<EMAIL>',
      password: 'password123',
      name: '<PERSON>',
      role: 'Agent Terra<PERSON>'
    }
  ];

  const testLogin = async (credentials) => {
    setLoading(true);
    try {
      console.log('Tentative de connexion avec:', credentials);
      
      const response = await authService.login({
        email: credentials.email,
        password: credentials.password
      });
      
      console.log('Réponse de connexion:', response.data);
      
      // Sauvegarder les données
      const { access, refresh, user } = response.data;
      
      tokenManager.setToken(access);
      tokenManager.setRefreshToken(refresh);
      userManager.setUser(user);
      
      toast.success(`Connexion réussie en tant que ${credentials.name}`);
      
      // Rediriger vers le dashboard
      navigate('/', { replace: true });
      
    } catch (error) {
      console.error('Erreur de connexion:', error);
      toast.error(`Erreur: ${error.response?.data?.detail || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testApiConnection = async () => {
    try {
      const response = await fetch('http://localhost:8001/api/v1/auth/token/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123'
        })
      });
      
      const data = await response.json();
      console.log('Test API direct:', data);
      
      if (response.ok) {
        toast.success('API accessible !');
      } else {
        toast.error(`Erreur API: ${data.detail || 'Erreur inconnue'}`);
      }
    } catch (error) {
      console.error('Erreur test API:', error);
      toast.error(`Erreur réseau: ${error.message}`);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-navy-900 via-navy-800 to-forest-900 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-heading font-bold text-white mb-2">
            Test de Connexion - GoldSentinel
          </h1>
          <p className="text-navy-200">
            Testez la connexion avec les utilisateurs par défaut
          </p>
        </div>

        {/* Test API */}
        <div className="mb-6">
          <Card className="bg-navy-800 border-navy-600">
            <Card.Body className="p-4 text-center">
              <h3 className="text-white font-semibold mb-2">Test de l'API</h3>
              <Button onClick={testApiConnection} variant="outline" className="text-white border-white">
                Tester la connexion API
              </Button>
            </Card.Body>
          </Card>
        </div>

        {/* User Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {testUsers.map((user, index) => (
            <Card key={index} className="hover:shadow-xl transition-all duration-200 border-navy-600">
              <Card.Body className="p-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-xl font-bold text-navy-900">
                      {user.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {user.name}
                  </h3>
                  
                  <p className="text-sm font-medium text-gold-600 mb-2">
                    {user.role}
                  </p>
                  
                  <div className="text-xs text-gray-500 mb-4">
                    <p><strong>Email:</strong> {user.email}</p>
                    <p><strong>Mot de passe:</strong> {user.password}</p>
                  </div>
                  
                  <Button 
                    onClick={() => testLogin(user)}
                    variant="primary"
                    className="w-full"
                    disabled={loading}
                  >
                    {loading ? 'Connexion...' : 'Se connecter'}
                  </Button>
                </div>
              </Card.Body>
            </Card>
          ))}
        </div>

        {/* Info */}
        <div className="mt-8 text-center">
          <Card className="bg-navy-800 border-navy-600">
            <Card.Body className="p-4">
              <p className="text-navy-200 text-sm">
                <strong className="text-gold-400">Test de connexion :</strong> 
                Utilisez ces comptes pour tester l'authentification avec le backend Django.
                Les mots de passe sont ceux définis dans la migration.
              </p>
            </Card.Body>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TestConnection;
