import React, { useState } from 'react';
import { Card, Button, Badge, Input } from '../components/ui';
import { 
  MagnifyingGlassIcon,
  UserIcon,
  MapIcon,
  ClockIcon,
  DocumentTextIcon,
  PlusIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { useApi } from '../hooks/useApi';
import { investigationService } from '../services/api';
import { formatDate, formatTimeAgo, formatCoordinates } from '../utils/formatters';
import toast from 'react-hot-toast';

const Investigations = () => {
  const [filters, setFilters] = useState({
    status: '',
    assigned_to: '',
    search: ''
  });

  const { data: investigations, loading, error, execute: loadInvestigations } = useApi(
    () => investigationService.getInvestigations(filters),
    { immediate: true }
  );

  const handleAssignInvestigation = async (investigationId, agentId) => {
    try {
      await investigationService.assignInvestigation(investigationId, agentId);
      toast.success('Investigation assignée avec succès');
      loadInvestigations();
    } catch (error) {
      toast.error('Erreur lors de l\'assignation');
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'PENDING': { variant: 'warning', label: 'En attente' },
      'ASSIGNED': { variant: 'info', label: 'Assignée' },
      'IN_PROGRESS': { variant: 'primary', label: 'En cours' },
      'COMPLETED': { variant: 'success', label: 'Terminée' },
      'CANCELLED': { variant: 'danger', label: 'Annulée' }
    };
    
    const config = statusConfig[status] || { variant: 'secondary', label: status };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getPriorityBadge = (priority) => {
    const priorityConfig = {
      'HIGH': { variant: 'danger', label: 'Haute', icon: '🔴' },
      'MEDIUM': { variant: 'warning', label: 'Moyenne', icon: '🟡' },
      'LOW': { variant: 'secondary', label: 'Basse', icon: '🟢' }
    };
    
    const config = priorityConfig[priority] || { variant: 'secondary', label: priority, icon: '⚪' };
    return (
      <Badge variant={config.variant}>
        <span className="mr-1">{config.icon}</span>
        {config.label}
      </Badge>
    );
  };

  const applyFilters = () => {
    loadInvestigations();
  };

  const clearFilters = () => {
    setFilters({ status: '', assigned_to: '', search: '' });
    setTimeout(() => loadInvestigations(), 100);
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement des investigations...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="border-red-200 bg-red-50">
          <Card.Body className="text-center py-12">
            <p className="text-red-600">Erreur lors du chargement des investigations</p>
            <Button onClick={loadInvestigations} className="mt-4">Réessayer</Button>
          </Card.Body>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-heading font-bold text-gray-900">
            Investigations Terrain
          </h1>
          <p className="text-gray-600">
            {investigations?.length || 0} investigation(s) en cours
          </p>
        </div>
        <Button variant="primary">
          <PlusIcon className="h-5 w-5 mr-2" />
          Nouvelle investigation
        </Button>
      </div>

      {/* Filtres */}
      <Card>
        <Card.Body>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Input
              placeholder="Rechercher..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            />
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gold-500"
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            >
              <option value="">Tous statuts</option>
              <option value="PENDING">En attente</option>
              <option value="ASSIGNED">Assignée</option>
              <option value="IN_PROGRESS">En cours</option>
              <option value="COMPLETED">Terminée</option>
              <option value="CANCELLED">Annulée</option>
            </select>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gold-500"
              value={filters.assigned_to}
              onChange={(e) => setFilters(prev => ({ ...prev, assigned_to: e.target.value }))}
            >
              <option value="">Tous agents</option>
              {/* TODO: Charger la liste des agents */}
            </select>
            <div className="flex space-x-2">
              <Button onClick={applyFilters} variant="primary" size="sm">
                Filtrer
              </Button>
              <Button onClick={clearFilters} variant="outline" size="sm">
                Effacer
              </Button>
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Liste des investigations */}
      {investigations && investigations.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {investigations.map((investigation) => (
            <Card key={investigation.id} className="hover:shadow-lg transition-shadow">
              <Card.Body>
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {investigation.title || `Investigation #${investigation.id}`}
                      </h3>
                      <p className="text-sm text-gray-500">
                        Créée le {formatDate(investigation.created_at)}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      {getStatusBadge(investigation.status)}
                      {investigation.priority && getPriorityBadge(investigation.priority)}
                    </div>
                  </div>

                  {/* Description */}
                  {investigation.description && (
                    <p className="text-gray-700 text-sm line-clamp-2">
                      {investigation.description}
                    </p>
                  )}

                  {/* Détails */}
                  <div className="space-y-2 text-sm text-gray-600">
                    {investigation.detection && (
                      <div className="flex items-center space-x-2">
                        <EyeIcon className="h-4 w-4" />
                        <span>Détection #{investigation.detection.id}</span>
                      </div>
                    )}
                    
                    {investigation.location && (
                      <div className="flex items-center space-x-2">
                        <MapIcon className="h-4 w-4" />
                        <span>
                          {typeof investigation.location === 'object' 
                            ? formatCoordinates(investigation.location.latitude, investigation.location.longitude)
                            : investigation.location
                          }
                        </span>
                      </div>
                    )}
                    
                    {investigation.assigned_to && (
                      <div className="flex items-center space-x-2">
                        <UserIcon className="h-4 w-4" />
                        <span>
                          Assignée à {investigation.assigned_to.first_name} {investigation.assigned_to.last_name}
                        </span>
                      </div>
                    )}
                    
                    {investigation.deadline && (
                      <div className="flex items-center space-x-2">
                        <ClockIcon className="h-4 w-4" />
                        <span>
                          Échéance: {formatDate(investigation.deadline)}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Progress */}
                  {investigation.progress !== undefined && (
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Progression</span>
                        <span className="font-medium">{investigation.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-gold-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${investigation.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex space-x-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <EyeIcon className="h-4 w-4 mr-1" />
                      Détails
                    </Button>
                    
                    {investigation.status === 'PENDING' && (
                      <Button variant="primary" size="sm">
                        <UserIcon className="h-4 w-4 mr-1" />
                        Assigner
                      </Button>
                    )}
                    
                    {investigation.status === 'IN_PROGRESS' && (
                      <Button variant="success" size="sm">
                        <DocumentTextIcon className="h-4 w-4 mr-1" />
                        Rapport
                      </Button>
                    )}
                  </div>
                </div>
              </Card.Body>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <Card.Body className="text-center py-12">
            <MagnifyingGlassIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucune investigation trouvée
            </h3>
            <p className="text-gray-500">
              Aucune investigation ne correspond à vos critères.
            </p>
          </Card.Body>
        </Card>
      )}
    </div>
  );
};

export default Investigations;
