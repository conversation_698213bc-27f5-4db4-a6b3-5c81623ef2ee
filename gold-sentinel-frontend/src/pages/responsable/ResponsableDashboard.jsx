import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  MapIcon,
  UsersIcon,
  EyeIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { Card, Badge, StatusIndicator, Button } from '../../components/ui';
import { useApi } from '../../hooks/useApi';
import { statsService } from '../../services/api';
import { formatNumber, formatCurrency, formatTimeAgo } from '../../utils/formatters';

const ResponsableDashboard = () => {
  const { data: stats, loading } = useApi(
    () => statsService.getRegionalStats('Bondoukou'),
    { immediate: true }
  );

  const regionalStats = [
    {
      title: 'Détections Région',
      value: formatNumber(stats?.regional_detections || 89),
      icon: EyeIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+5 cette semaine',
      trend: 'up'
    },
    {
      title: 'Alertes Actives',
      value: stats?.active_alerts || 12,
      icon: ExclamationTriangleIcon,
      color: 'text-alert-600',
      bgColor: 'bg-alert-50',
      change: '-3 depuis hier',
      trend: 'down'
    },
    {
      title: 'Investigations',
      value: stats?.ongoing_investigations || 8,
      icon: MagnifyingGlassIcon,
      color: 'text-forest-600',
      bgColor: 'bg-forest-50',
      change: '2 en cours',
      trend: 'neutral'
    },
    {
      title: 'Équipe Terrain',
      value: stats?.field_agents || 15,
      icon: UsersIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '12 disponibles',
      trend: 'up'
    }
  ];

  const teamMembers = [
    {
      id: 1,
      name: 'Kouassi Jean',
      role: 'Agent Terrain',
      status: 'active',
      location: 'Bondoukou Centre',
      lastActivity: new Date(Date.now() - 30 * 60 * 1000)
    },
    {
      id: 2,
      name: 'Diabaté Marie',
      role: 'Agent Analyste',
      status: 'active',
      location: 'Bureau Régional',
      lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
      id: 3,
      name: 'Traoré Ibrahim',
      role: 'Agent Terrain',
      status: 'mission',
      location: 'Tanda',
      lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000)
    },
    {
      id: 4,
      name: 'Koné Fatou',
      role: 'Agent Technique',
      status: 'active',
      location: 'Bureau Régional',
      lastActivity: new Date(Date.now() - 1 * 60 * 60 * 1000)
    }
  ];

  const pendingValidations = [
    {
      id: 1,
      type: 'detection',
      title: 'Site minier suspect - Tanda',
      priority: 'high',
      submittedBy: 'Agent Kouassi',
      submittedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      confidence: 0.87
    },
    {
      id: 2,
      type: 'detection',
      title: 'Pollution cours d\'eau - Bondoukou',
      priority: 'medium',
      submittedBy: 'Agent Diabaté',
      submittedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
      confidence: 0.72
    },
    {
      id: 3,
      type: 'investigation',
      title: 'Rapport terrain - Zone forestière',
      priority: 'low',
      submittedBy: 'Agent Traoré',
      submittedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      confidence: null
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'mission': return 'warning';
      case 'offline': return 'danger';
      default: return 'info';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'active': return 'Disponible';
      case 'mission': return 'En mission';
      case 'offline': return 'Hors ligne';
      default: return 'Inconnu';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'danger';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'info';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-forest-800 to-forest-700 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-heading font-bold">
              Région de Bondoukou
            </h1>
            <p className="text-forest-200 mt-1">
              Responsable Régional - Surveillance et coordination
            </p>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-2">
              <MapIcon className="h-5 w-5 text-forest-200" />
              <span className="text-forest-200">Côte d'Ivoire</span>
            </div>
            <p className="text-gold-400 font-medium mt-1">
              {new Date().toLocaleString('fr-FR')}
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {regionalStats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title} className="hover:shadow-lg transition-shadow duration-200">
              <Card.Body className="p-6">
                <div className="flex items-center">
                  <div className={`${stat.bgColor} p-3 rounded-lg`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className="ml-4 flex-1">
                    <p className="text-sm font-medium text-gray-600">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-navy-900">
                      {stat.value}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {stat.change}
                    </p>
                  </div>
                </div>
              </Card.Body>
            </Card>
          );
        })}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Team Status */}
        <Card>
          <Card.Header>
            <Card.Title>Équipe Régionale</Card.Title>
            <Card.Description>
              État et localisation des agents
            </Card.Description>
          </Card.Header>
          <Card.Body>
            <div className="space-y-4">
              {teamMembers.map((member) => (
                <div key={member.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <StatusIndicator status={member.status} showLabel={false} />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {member.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {member.role} • {member.location}
                      </p>
                      <p className="text-xs text-gray-400">
                        Dernière activité: {formatTimeAgo(member.lastActivity)}
                      </p>
                    </div>
                  </div>
                  <Badge variant={getStatusColor(member.status)} size="sm">
                    {getStatusLabel(member.status)}
                  </Badge>
                </div>
              ))}
            </div>
          </Card.Body>
          <Card.Footer>
            <Link to="/responsable/teams">
              <Button variant="outline" size="sm" className="w-full">
                Gérer l'équipe
              </Button>
            </Link>
          </Card.Footer>
        </Card>

        {/* Pending Validations */}
        <Card>
          <Card.Header>
            <Card.Title>Validations en Attente</Card.Title>
            <Card.Description>
              Détections et rapports à valider
            </Card.Description>
          </Card.Header>
          <Card.Body>
            <div className="space-y-4">
              {pendingValidations.map((item) => (
                <div key={item.id} className="border border-gray-200 rounded-lg p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="text-sm font-medium text-gray-900">
                          {item.title}
                        </h4>
                        <Badge variant={getPriorityColor(item.priority)} size="sm">
                          {item.priority}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Par {item.submittedBy} • {formatTimeAgo(item.submittedAt)}
                      </p>
                      {item.confidence && (
                        <p className="text-xs text-gray-600 mt-1">
                          Confiance: {Math.round(item.confidence * 100)}%
                        </p>
                      )}
                    </div>
                    <div className="flex space-x-1">
                      <Button variant="success" size="sm">
                        <CheckCircleIcon className="h-4 w-4" />
                      </Button>
                      <Button variant="danger" size="sm">
                        ✕
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card.Body>
          <Card.Footer>
            <Link to="/responsable/validations">
              <Button variant="outline" size="sm" className="w-full">
                Voir toutes les validations
              </Button>
            </Link>
          </Card.Footer>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link to="/responsable/detections">
          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            <Card.Body className="p-6 text-center">
              <EyeIcon className="h-8 w-8 text-blue-600 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900">Détections</h3>
              <p className="text-sm text-gray-500 mt-1">
                Valider et gérer les détections
              </p>
            </Card.Body>
          </Card>
        </Link>

        <Link to="/responsable/teams">
          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            <Card.Body className="p-6 text-center">
              <UsersIcon className="h-8 w-8 text-purple-600 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900">Équipes</h3>
              <p className="text-sm text-gray-500 mt-1">
                Gérer les agents régionaux
              </p>
            </Card.Body>
          </Card>
        </Link>

        <Link to="/responsable/reports">
          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            <Card.Body className="p-6 text-center">
              <DocumentTextIcon className="h-8 w-8 text-green-600 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900">Rapports</h3>
              <p className="text-sm text-gray-500 mt-1">
                Générer des rapports régionaux
              </p>
            </Card.Body>
          </Card>
        </Link>
      </div>
    </div>
  );
};

export default ResponsableDashboard;
