import React, { useState, useEffect } from 'react';
import { Card, Button, Badge, Input } from '../components/ui';
import { 
  EyeIcon, 
  MapIcon, 
  CheckCircleIcon, 
  XMarkIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';
import { useApi } from '../hooks/useApi';
import { detectionService } from '../services/api';
import { formatDate, formatCoordinates } from '../utils/formatters';
import toast from 'react-hot-toast';

const Detections = () => {
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    confidence_min: '',
    date_from: '',
    date_to: ''
  });

  const { data: detections, loading, error, execute: loadDetections } = useApi(
    () => detectionService.getDetections(filters),
    { immediate: true }
  );

  const handleValidateDetection = async (detectionId) => {
    try {
      await detectionService.validateDetection(detectionId);
      toast.success('Détection validée avec succès');
      loadDetections(); // Recharger les données
    } catch (error) {
      toast.error('Erreur lors de la validation');
    }
  };

  const handleInvalidateDetection = async (detectionId) => {
    try {
      await detectionService.invalidateDetection(detectionId);
      toast.success('Détection invalidée');
      loadDetections(); // Recharger les données
    } catch (error) {
      toast.error('Erreur lors de l\'invalidation');
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'PENDING': { variant: 'warning', label: 'En attente' },
      'VALIDATED': { variant: 'success', label: 'Validée' },
      'INVALIDATED': { variant: 'danger', label: 'Invalidée' },
      'INVESTIGATING': { variant: 'info', label: 'En investigation' }
    };
    
    const config = statusConfig[status] || { variant: 'secondary', label: status };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const applyFilters = () => {
    loadDetections();
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      status: '',
      confidence_min: '',
      date_from: '',
      date_to: ''
    });
    setTimeout(() => loadDetections(), 100);
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement des détections...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="border-red-200 bg-red-50">
          <Card.Body className="text-center py-12">
            <p className="text-red-600">Erreur lors du chargement des détections</p>
            <Button onClick={loadDetections} className="mt-4">
              Réessayer
            </Button>
          </Card.Body>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-heading font-bold text-gray-900">
            Détections Satellites
          </h1>
          <p className="text-gray-600">
            {detections?.length || 0} détection(s) trouvée(s)
          </p>
        </div>
        <Button variant="primary">
          <PhotoIcon className="h-5 w-5 mr-2" />
          Nouvelle analyse
        </Button>
      </div>

      {/* Filtres */}
      <Card>
        <Card.Header>
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-500" />
            <span className="font-medium">Filtres</span>
          </div>
        </Card.Header>
        <Card.Body>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <Input
                placeholder="Rechercher..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                icon={MagnifyingGlassIcon}
              />
            </div>
            <div>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gold-500"
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <option value="">Tous les statuts</option>
                <option value="PENDING">En attente</option>
                <option value="VALIDATED">Validées</option>
                <option value="INVALIDATED">Invalidées</option>
                <option value="INVESTIGATING">En investigation</option>
              </select>
            </div>
            <div>
              <Input
                type="number"
                placeholder="Confiance min"
                value={filters.confidence_min}
                onChange={(e) => handleFilterChange('confidence_min', e.target.value)}
                min="0"
                max="1"
                step="0.1"
              />
            </div>
            <div>
              <Input
                type="date"
                placeholder="Date début"
                value={filters.date_from}
                onChange={(e) => handleFilterChange('date_from', e.target.value)}
              />
            </div>
            <div className="flex space-x-2">
              <Button onClick={applyFilters} variant="primary" size="sm">
                Appliquer
              </Button>
              <Button onClick={clearFilters} variant="outline" size="sm">
                Effacer
              </Button>
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Liste des détections */}
      {detections && detections.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {detections.map((detection) => (
            <Card key={detection.id} className="hover:shadow-lg transition-shadow">
              <Card.Body>
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        Détection #{detection.id}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {formatDate(detection.created_at)}
                      </p>
                    </div>
                    {getStatusBadge(detection.status)}
                  </div>

                  {/* Confiance */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Confiance :</span>
                    <span className={`font-semibold ${getConfidenceColor(detection.confidence_score)}`}>
                      {Math.round(detection.confidence_score * 100)}%
                    </span>
                  </div>

                  {/* Coordonnées */}
                  <div className="text-sm text-gray-600">
                    <div className="flex items-center space-x-1">
                      <MapIcon className="h-4 w-4" />
                      <span>
                        {formatCoordinates(detection.latitude, detection.longitude)}
                      </span>
                    </div>
                  </div>

                  {/* Description */}
                  {detection.description && (
                    <p className="text-sm text-gray-700 line-clamp-2">
                      {detection.description}
                    </p>
                  )}

                  {/* Actions */}
                  <div className="flex space-x-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <EyeIcon className="h-4 w-4 mr-1" />
                      Détails
                    </Button>
                    {detection.status === 'PENDING' && (
                      <>
                        <Button 
                          variant="success" 
                          size="sm"
                          onClick={() => handleValidateDetection(detection.id)}
                        >
                          <CheckCircleIcon className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="danger" 
                          size="sm"
                          onClick={() => handleInvalidateDetection(detection.id)}
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </Card.Body>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <Card.Body className="text-center py-12">
            <EyeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucune détection trouvée
            </h3>
            <p className="text-gray-500">
              Aucune détection ne correspond à vos critères de recherche.
            </p>
          </Card.Body>
        </Card>
      )}
    </div>
  );
};

export default Detections;
