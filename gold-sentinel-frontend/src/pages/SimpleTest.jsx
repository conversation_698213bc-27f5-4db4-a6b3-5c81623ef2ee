import React from 'react';
import { userManager } from '../utils/auth';

const SimpleTest = () => {
  const userType = userManager.getUserType();
  const user = userManager.getUser();

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Test Simple</h1>
      
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-lg font-semibold mb-4">Informations Utilisateur</h2>
        
        <div className="space-y-2">
          <p><strong>Type d'utilisateur:</strong> {userType || 'Non défini'}</p>
          <p><strong>Utilisateur:</strong> {user ? JSON.stringify(user, null, 2) : 'Aucun'}</p>
        </div>
        
        <div className="mt-6">
          <button 
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Recharger
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimpleTest;
