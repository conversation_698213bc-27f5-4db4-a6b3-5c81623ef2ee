import React from 'react';
import { Link } from 'react-router-dom';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { <PERSON><PERSON>, Card } from '../components/ui';

const Unauthorized = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <ExclamationTriangleIcon className="h-16 w-16 text-alert-600" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-heading font-bold text-navy-900">
          Accès non autorisé
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Vous n'avez pas les permissions nécessaires pour accéder à cette page.
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <Card className="py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Que souhaitez-vous faire ?
            </h3>
            <div className="space-y-3">
              <Link to="/" className="block">
                <Button variant="primary" className="w-full">
                  Retour au tableau de bord
                </Button>
              </Link>
              <Link to="/profile" className="block">
                <Button variant="outline" className="w-full">
                  Voir mon profil
                </Button>
              </Link>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Unauthorized;
