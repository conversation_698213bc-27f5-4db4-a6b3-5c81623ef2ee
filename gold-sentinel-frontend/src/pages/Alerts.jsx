import React, { useState } from 'react';
import { Card, Button, Badge, Input } from '../components/ui';
import { 
  ExclamationTriangleIcon,
  BellIcon,
  CheckCircleIcon,
  UserIcon,
  ClockIcon,
  MapIcon
} from '@heroicons/react/24/outline';
import { useApi } from '../hooks/useApi';
import { alertService } from '../services/api';
import { formatDate, formatTimeAgo } from '../utils/formatters';
import toast from 'react-hot-toast';

const Alerts = () => {
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    search: ''
  });

  const { data: alerts, loading, error, execute: loadAlerts } = useApi(
    () => alertService.getAlerts(filters),
    { immediate: true }
  );

  const handleMarkAsRead = async (alertId) => {
    try {
      await alertService.markAsRead(alertId);
      toast.success('Alerte marquée comme lue');
      loadAlerts();
    } catch (error) {
      toast.error('Erreur lors du marquage');
    }
  };

  const handleAssignAlert = async (alertId, agentId) => {
    try {
      await alertService.assignAlert(alertId, agentId);
      toast.success('Alerte assignée avec succès');
      loadAlerts();
    } catch (error) {
      toast.error('Erreur lors de l\'assignation');
    }
  };

  const getPriorityBadge = (priority) => {
    const priorityConfig = {
      'CRITICAL': { variant: 'danger', label: 'Critique', icon: '🔴' },
      'HIGH': { variant: 'warning', label: 'Haute', icon: '🟠' },
      'MEDIUM': { variant: 'info', label: 'Moyenne', icon: '🟡' },
      'LOW': { variant: 'secondary', label: 'Basse', icon: '🟢' }
    };
    
    const config = priorityConfig[priority] || { variant: 'secondary', label: priority, icon: '⚪' };
    return (
      <Badge variant={config.variant}>
        <span className="mr-1">{config.icon}</span>
        {config.label}
      </Badge>
    );
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'ACTIVE': { variant: 'danger', label: 'Active' },
      'ACKNOWLEDGED': { variant: 'warning', label: 'Acquittée' },
      'RESOLVED': { variant: 'success', label: 'Résolue' },
      'ASSIGNED': { variant: 'info', label: 'Assignée' }
    };
    
    const config = statusConfig[status] || { variant: 'secondary', label: status };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const applyFilters = () => {
    loadAlerts();
  };

  const clearFilters = () => {
    setFilters({ status: '', priority: '', search: '' });
    setTimeout(() => loadAlerts(), 100);
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement des alertes...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="border-red-200 bg-red-50">
          <Card.Body className="text-center py-12">
            <p className="text-red-600">Erreur lors du chargement des alertes</p>
            <Button onClick={loadAlerts} className="mt-4">Réessayer</Button>
          </Card.Body>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-heading font-bold text-gray-900">
            Alertes Système
          </h1>
          <p className="text-gray-600">
            {alerts?.length || 0} alerte(s) active(s)
          </p>
        </div>
        <Button variant="primary">
          <BellIcon className="h-5 w-5 mr-2" />
          Nouvelle alerte
        </Button>
      </div>

      {/* Filtres */}
      <Card>
        <Card.Body>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Input
              placeholder="Rechercher..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            />
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gold-500"
              value={filters.priority}
              onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
            >
              <option value="">Toutes priorités</option>
              <option value="CRITICAL">Critique</option>
              <option value="HIGH">Haute</option>
              <option value="MEDIUM">Moyenne</option>
              <option value="LOW">Basse</option>
            </select>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gold-500"
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            >
              <option value="">Tous statuts</option>
              <option value="ACTIVE">Active</option>
              <option value="ACKNOWLEDGED">Acquittée</option>
              <option value="RESOLVED">Résolue</option>
              <option value="ASSIGNED">Assignée</option>
            </select>
            <div className="flex space-x-2">
              <Button onClick={applyFilters} variant="primary" size="sm">
                Filtrer
              </Button>
              <Button onClick={clearFilters} variant="outline" size="sm">
                Effacer
              </Button>
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Liste des alertes */}
      {alerts && alerts.length > 0 ? (
        <div className="space-y-4">
          {alerts.map((alert) => (
            <Card key={alert.id} className={`border-l-4 ${
              alert.priority === 'CRITICAL' ? 'border-l-red-500' :
              alert.priority === 'HIGH' ? 'border-l-orange-500' :
              alert.priority === 'MEDIUM' ? 'border-l-yellow-500' :
              'border-l-green-500'
            }`}>
              <Card.Body>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                      <h3 className="font-semibold text-gray-900">
                        {alert.title}
                      </h3>
                      {getPriorityBadge(alert.priority)}
                      {getStatusBadge(alert.status)}
                    </div>
                    
                    <p className="text-gray-700 mb-3">
                      {alert.description}
                    </p>
                    
                    <div className="flex items-center space-x-6 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <ClockIcon className="h-4 w-4" />
                        <span>{formatTimeAgo(alert.created_at)}</span>
                      </div>
                      
                      {alert.location && (
                        <div className="flex items-center space-x-1">
                          <MapIcon className="h-4 w-4" />
                          <span>{alert.location}</span>
                        </div>
                      )}
                      
                      {alert.assigned_to && (
                        <div className="flex items-center space-x-1">
                          <UserIcon className="h-4 w-4" />
                          <span>Assignée à {alert.assigned_to.first_name}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2 ml-4">
                    {alert.status === 'ACTIVE' && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleMarkAsRead(alert.id)}
                      >
                        <CheckCircleIcon className="h-4 w-4 mr-1" />
                        Acquitter
                      </Button>
                    )}
                    
                    <Button variant="primary" size="sm">
                      Détails
                    </Button>
                  </div>
                </div>
              </Card.Body>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <Card.Body className="text-center py-12">
            <BellIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucune alerte trouvée
            </h3>
            <p className="text-gray-500">
              Aucune alerte ne correspond à vos critères.
            </p>
          </Card.Body>
        </Card>
      )}
    </div>
  );
};

export default Alerts;
