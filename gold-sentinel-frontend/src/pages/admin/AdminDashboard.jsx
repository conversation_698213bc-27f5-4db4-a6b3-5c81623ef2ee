import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  UsersIcon,
  ChartBarIcon,
  CogIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  ServerIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { Card, Badge, StatusIndicator, Button } from '../../components/ui';
import { useApi } from '../../hooks/useApi';
import { statsService } from '../../services/api';
import { formatNumber, formatCurrency, formatTimeAgo } from '../../utils/formatters';

const AdminDashboard = () => {
  const [systemHealth, setSystemHealth] = useState([
    { name: 'API Backend', status: 'active', uptime: '99.9%' },
    { name: 'Base de données', status: 'active', uptime: '99.8%' },
    { name: 'Service IA', status: 'warning', uptime: '95.2%' },
    { name: 'Stockage', status: 'active', uptime: '99.9%' }
  ]);

  const { data: stats, loading, execute: loadStats } = useApi(
    statsService.getDashboardStats,
    { immediate: true }
  );

  const adminStats = [
    {
      title: 'Utilisateurs Actifs',
      value: stats?.active_users || 45,
      icon: UsersIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+12%',
      changeType: 'positive'
    },
    {
      title: 'Détections Totales',
      value: formatNumber(stats?.total_detections || 1247),
      icon: EyeIcon,
      color: 'text-forest-600',
      bgColor: 'bg-forest-50',
      change: '+8%',
      changeType: 'positive'
    },
    {
      title: 'Alertes Critiques',
      value: stats?.critical_alerts || 23,
      icon: ExclamationTriangleIcon,
      color: 'text-alert-600',
      bgColor: 'bg-alert-50',
      change: '-15%',
      changeType: 'negative'
    },
    {
      title: 'Pertes Évitées',
      value: formatCurrency(stats?.prevented_losses || 15000000),
      icon: ShieldCheckIcon,
      color: 'text-gold-600',
      bgColor: 'bg-gold-50',
      change: '+25%',
      changeType: 'positive'
    }
  ];

  const quickActions = [
    {
      title: 'Gestion Utilisateurs',
      description: 'Ajouter, modifier ou désactiver des utilisateurs',
      href: '/admin/users',
      icon: UsersIcon,
      color: 'bg-blue-500'
    },
    {
      title: 'Configuration Système',
      description: 'Paramètres globaux et configuration',
      href: '/admin/system',
      icon: CogIcon,
      color: 'bg-gray-500'
    },
    {
      title: 'Journaux Système',
      description: 'Consulter les logs et événements',
      href: '/admin/logs',
      icon: DocumentTextIcon,
      color: 'bg-green-500'
    },
    {
      title: 'Monitoring',
      description: 'État des services et performances',
      href: '/admin/monitoring',
      icon: ServerIcon,
      color: 'bg-purple-500'
    }
  ];

  const recentActivity = [
    {
      id: 1,
      type: 'user_created',
      message: 'Nouvel utilisateur créé : Agent Terrain Korhogo',
      time: new Date(Date.now() - 30 * 60 * 1000),
      severity: 'info'
    },
    {
      id: 2,
      type: 'system_alert',
      message: 'Service IA redémarré automatiquement',
      time: new Date(Date.now() - 2 * 60 * 60 * 1000),
      severity: 'warning'
    },
    {
      id: 3,
      type: 'detection_validated',
      message: '15 nouvelles détections validées',
      time: new Date(Date.now() - 4 * 60 * 60 * 1000),
      severity: 'success'
    }
  ];

  const getActivityBadgeVariant = (severity) => {
    switch (severity) {
      case 'success': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'danger';
      case 'info': return 'info';
      default: return 'info';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-navy-900 to-navy-800 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-heading font-bold">
              Administration GoldSentinel
            </h1>
            <p className="text-navy-200 mt-1">
              Tableau de bord administrateur - Vue d'ensemble du système
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-navy-200">Dernière mise à jour</p>
            <p className="text-gold-400 font-medium">
              {new Date().toLocaleString('fr-FR')}
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {adminStats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title} className="hover:shadow-lg transition-shadow duration-200">
              <Card.Body className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`${stat.bgColor} p-3 rounded-lg`}>
                      <Icon className={`h-6 w-6 ${stat.color}`} />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">
                        {stat.title}
                      </p>
                      <p className="text-2xl font-bold text-navy-900">
                        {stat.value}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge 
                      variant={stat.changeType === 'positive' ? 'success' : 'danger'}
                      size="sm"
                    >
                      {stat.change}
                    </Badge>
                  </div>
                </div>
              </Card.Body>
            </Card>
          );
        })}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Health */}
        <Card>
          <Card.Header>
            <Card.Title>État du Système</Card.Title>
            <Card.Description>
              Monitoring des services critiques
            </Card.Description>
          </Card.Header>
          <Card.Body>
            <div className="space-y-4">
              {systemHealth.map((service, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <StatusIndicator status={service.status} showLabel={false} />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {service.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        Uptime: {service.uptime}
                      </p>
                    </div>
                  </div>
                  <Badge 
                    variant={service.status === 'active' ? 'success' : 'warning'}
                  >
                    {service.status === 'active' ? 'Opérationnel' : 'Attention'}
                  </Badge>
                </div>
              ))}
            </div>
          </Card.Body>
          <Card.Footer>
            <Button variant="outline" size="sm" className="w-full">
              Voir détails monitoring
            </Button>
          </Card.Footer>
        </Card>

        {/* Recent Activity */}
        <Card>
          <Card.Header>
            <Card.Title>Activité Récente</Card.Title>
            <Card.Description>
              Derniers événements système
            </Card.Description>
          </Card.Header>
          <Card.Body>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    <div className={`w-2 h-2 rounded-full ${
                      activity.severity === 'success' ? 'bg-green-500' :
                      activity.severity === 'warning' ? 'bg-yellow-500' :
                      activity.severity === 'error' ? 'bg-red-500' :
                      'bg-blue-500'
                    }`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900">
                      {activity.message}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatTimeAgo(activity.time)}
                    </p>
                  </div>
                  <Badge variant={getActivityBadgeVariant(activity.severity)} size="sm">
                    {activity.severity}
                  </Badge>
                </div>
              ))}
            </div>
          </Card.Body>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <Card.Header>
          <Card.Title>Actions Rapides</Card.Title>
          <Card.Description>
            Accès direct aux fonctions d'administration
          </Card.Description>
        </Card.Header>
        <Card.Body>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => {
              const Icon = action.icon;
              return (
                <Link
                  key={action.title}
                  to={action.href}
                  className="group p-4 rounded-lg border border-gray-200 hover:border-gold-400 hover:shadow-md transition-all duration-200"
                >
                  <div className="flex items-center space-x-3">
                    <div className={`${action.color} p-2 rounded-lg text-white group-hover:scale-110 transition-transform duration-200`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 group-hover:text-gold-600">
                        {action.title}
                      </h3>
                      <p className="text-xs text-gray-500 mt-1">
                        {action.description}
                      </p>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default AdminDashboard;
