import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { Toaster } from 'react-hot-toast';
import { store } from './store';
import MainLayout from './components/layout/MainLayout';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Unauthorized from './pages/Unauthorized';
import TestComponent from './components/TestComponent';
import TestAuth from './pages/TestAuth';
import SimpleTest from './pages/SimpleTest';
import './App.css';

function App() {
  return (
    <Provider store={store}>
      <Router>
        <Routes>
          {/* Route publique */}
          <Route path="/login" element={<Login />} />
          <Route path="/unauthorized" element={<Unauthorized />} />

          {/* Routes protégées */}
          <Route path="/" element={
            <ProtectedRoute>
              <MainLayout />
            </ProtectedRoute>
          }>
            {/* Dashboard par défaut */}
            <Route index element={<Dashboard />} />

            {/* Routes communes */}
            <Route path="detections" element={<div>Détections</div>} />
            <Route path="alerts" element={<div>Alertes</div>} />
            <Route path="investigations" element={<div>Investigations</div>} />
            <Route path="financial-risks" element={<div>Risques Financiers</div>} />
            <Route path="images" element={<div>Images Satellites</div>} />
            <Route path="stats" element={<div>Statistiques</div>} />

            {/* Routes spécifiques par type d'utilisateur */}
            <Route path="admin/*" element={<div>Admin Routes</div>} />
            <Route path="responsable/*" element={<div>Responsable Routes</div>} />
            <Route path="terrain/*" element={<div>Terrain Routes</div>} />
            <Route path="technique/*" element={<div>Technique Routes</div>} />
            <Route path="analyste/*" element={<div>Analyste Routes</div>} />

            {/* Routes utilisateur */}
            <Route path="profile" element={<div>Profil</div>} />
            <Route path="settings" element={<div>Paramètres</div>} />
            <Route path="test" element={<TestComponent />} />
            <Route path="test-auth" element={<TestAuth />} />
            <Route path="simple-test" element={<SimpleTest />} />
          </Route>

          {/* Redirection par défaut */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>

        {/* Toast notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              style: {
                background: '#10B981',
              },
            },
            error: {
              style: {
                background: '#EF4444',
              },
            },
          }}
        />
      </Router>
    </Provider>
  );
}

export default App;
