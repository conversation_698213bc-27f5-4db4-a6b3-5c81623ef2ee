import { useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';

export const useApi = (apiFunction, options = {}) => {
  const {
    immediate = false,
    showSuccessToast = false,
    showErrorToast = true,
    successMessage = 'Opération réussie',
    onSuccess,
    onError,
    dependencies = []
  } = options;

  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const execute = useCallback(async (...args) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiFunction(...args);
      const result = response.data;
      
      setData(result);
      
      if (showSuccessToast) {
        toast.success(successMessage);
      }
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return { success: true, data: result };
    } catch (err) {
      const errorMessage = err.response?.data?.detail || 
                          err.response?.data?.message || 
                          err.message || 
                          'Une erreur est survenue';
      
      setError(errorMessage);
      
      if (showErrorToast) {
        toast.error(errorMessage);
      }
      
      if (onError) {
        onError(err);
      }
      
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [apiFunction, showSuccessToast, showErrorToast, successMessage, onSuccess, onError]);

  // Exécution automatique si immediate = true
  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate, execute, ...dependencies]);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return {
    data,
    loading,
    error,
    execute,
    reset
  };
};

// Hook spécialisé pour les listes avec pagination
export const useApiList = (apiFunction, options = {}) => {
  const {
    pageSize = 20,
    initialParams = {},
    ...apiOptions
  } = options;

  const [params, setParams] = useState({
    page: 1,
    page_size: pageSize,
    ...initialParams
  });

  const { data, loading, error, execute, reset } = useApi(
    () => apiFunction(params),
    {
      ...apiOptions,
      dependencies: [params]
    }
  );

  const loadPage = useCallback((page) => {
    setParams(prev => ({ ...prev, page }));
  }, []);

  const updateParams = useCallback((newParams) => {
    setParams(prev => ({ ...prev, ...newParams, page: 1 }));
  }, []);

  const refresh = useCallback(() => {
    execute();
  }, [execute]);

  return {
    data: data?.results || [],
    totalCount: data?.count || 0,
    totalPages: data?.total_pages || 0,
    currentPage: params.page,
    hasNext: !!data?.next,
    hasPrevious: !!data?.previous,
    loading,
    error,
    params,
    loadPage,
    updateParams,
    refresh,
    reset
  };
};

// Hook pour les mutations (POST, PUT, DELETE)
export const useMutation = (apiFunction, options = {}) => {
  const {
    showSuccessToast = true,
    showErrorToast = true,
    successMessage = 'Opération réussie',
    onSuccess,
    onError
  } = options;

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const mutate = useCallback(async (data) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiFunction(data);
      const result = response.data;
      
      if (showSuccessToast) {
        toast.success(successMessage);
      }
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return { success: true, data: result };
    } catch (err) {
      const errorMessage = err.response?.data?.detail || 
                          err.response?.data?.message || 
                          err.message || 
                          'Une erreur est survenue';
      
      setError(errorMessage);
      
      if (showErrorToast) {
        toast.error(errorMessage);
      }
      
      if (onError) {
        onError(err);
      }
      
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [apiFunction, showSuccessToast, showErrorToast, successMessage, onSuccess, onError]);

  const reset = useCallback(() => {
    setError(null);
    setLoading(false);
  }, []);

  return {
    mutate,
    loading,
    error,
    reset
  };
};
