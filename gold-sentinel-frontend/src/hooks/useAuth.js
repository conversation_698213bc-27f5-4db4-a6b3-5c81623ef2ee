import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { login, logout, setCredentials } from '../store/slices/authSlice';
import { userManager, tokenManager, isAuthenticated, hasPermission, canAccessRoute } from '../utils/auth';

export const useAuth = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { 
    user, 
    token, 
    refreshToken, 
    isAuthenticated: isAuthenticatedState, 
    loading, 
    error 
  } = useSelector((state) => state.auth);

  // Actions d'authentification
  const handleLogin = async (credentials) => {
    try {
      const result = await dispatch(login(credentials)).unwrap();
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error };
    }
  };

  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  const setUserCredentials = (userData) => {
    dispatch(setCredentials(userData));
  };

  // Informations utilisateur
  const currentUser = user || userManager.getUser();
  const userType = userManager.getUserType();
  const userAuthorities = userManager.getUserAuthorities();
  const userPermissions = userManager.getUserPermissions();

  // Vérifications de permissions
  const checkPermission = (permission) => {
    return hasPermission(permission);
  };

  const checkRouteAccess = (route) => {
    return canAccessRoute(route);
  };

  const checkAuthority = (authority) => {
    return userManager.hasAuthority(authority);
  };

  // État d'authentification
  const isLoggedIn = isAuthenticated() || isAuthenticatedState;
  const hasValidToken = token && tokenManager.getToken();

  return {
    // État
    user: currentUser,
    userType,
    userAuthorities,
    userPermissions,
    isAuthenticated: isLoggedIn,
    hasValidToken,
    loading,
    error,
    
    // Actions
    login: handleLogin,
    logout: handleLogout,
    setCredentials: setUserCredentials,
    
    // Vérifications
    hasPermission: checkPermission,
    canAccessRoute: checkRouteAccess,
    hasAuthority: checkAuthority,
  };
};
