import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api/v1';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Ajouter un intercepteur pour gérer les tokens
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const authService = {
  login: async (credentials) => {
    return api.post('/auth/token/', credentials);
  },
  refreshToken: async (refreshToken) => {
    return api.post('/auth/token/refresh/', { refresh: refreshToken });
  },
};

export const detectionService = {
  getDetections: async (params) => {
    return api.get('/detections/', { params });
  },
  createDetection: async (data) => {
    return api.post('/detections/', data);
  },
};

export const alertService = {
  getAlerts: async (params) => {
    return api.get('/alerts/', { params });
  },
};

export const investigationService = {
  getInvestigations: async (params) => {
    return api.get('/investigations/', { params });
  },
  getAvailableAgents: async () => {
    return api.get('/investigations/available-agents/');
  },
};

export const financialRiskService = {
  getFinancialRisks: async (params) => {
    return api.get('/financial-risks/', { params });
  },
  getHighImpactRisks: async () => {
    return api.get('/financial-risks/high-impact/');
  },
};

export const analysisService = {
  runAnalysis: async (params) => {
    return api.post('/analysis/run/', params);
  },
};

export const statsService = {
  getDashboardStats: async () => {
    return api.get('/stats/dashboard/');
  },
  getDetectionTrends: async (days) => {
    return api.get('/stats/detection-trends/', { params: { days } });
  },
};

export default api;
