import axios from 'axios';
import { API_BASE_URL, API_ENDPOINTS, HTTP_STATUS, ERROR_MESSAGES } from '../constants/api';
import { tokenManager, isTokenExpired } from '../utils/auth';
import toast from 'react-hot-toast';

// Instance Axios principale
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 secondes
});

// Instance pour les requêtes de refresh (sans intercepteurs)
const refreshApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Variable pour éviter les appels multiples de refresh
let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

// Intercepteur de requête - Ajouter le token
api.interceptors.request.use(
  (config) => {
    const token = tokenManager.getToken();
    if (token && !isTokenExpired(token)) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur de réponse - Gestion des erreurs et refresh automatique
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Si erreur 401 et pas déjà en train de refresh
    if (error.response?.status === HTTP_STATUS.UNAUTHORIZED && !originalRequest._retry) {
      if (isRefreshing) {
        // Si déjà en train de refresh, mettre en queue
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return api(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      const refreshToken = tokenManager.getRefreshToken();

      if (refreshToken) {
        try {
          const response = await refreshApi.post(API_ENDPOINTS.AUTH.REFRESH, {
            refresh: refreshToken
          });

          const { access } = response.data;
          tokenManager.setToken(access);

          processQueue(null, access);

          originalRequest.headers.Authorization = `Bearer ${access}`;
          return api(originalRequest);
        } catch (refreshError) {
          processQueue(refreshError, null);

          // Refresh failed, redirect to login
          tokenManager.clearAll();
          window.location.href = '/login';

          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      } else {
        // No refresh token, redirect to login
        tokenManager.clearAll();
        window.location.href = '/login';
        return Promise.reject(error);
      }
    }

    // Gestion des autres erreurs
    const errorMessage = getErrorMessage(error);

    // Afficher toast d'erreur sauf pour certaines requêtes
    if (!originalRequest.skipErrorToast) {
      toast.error(errorMessage);
    }

    return Promise.reject(error);
  }
);

// Fonction pour extraire le message d'erreur
const getErrorMessage = (error) => {
  if (error.response) {
    const { status, data } = error.response;

    switch (status) {
      case HTTP_STATUS.BAD_REQUEST:
        return data.detail || data.message || ERROR_MESSAGES.VALIDATION_ERROR;
      case HTTP_STATUS.UNAUTHORIZED:
        return ERROR_MESSAGES.UNAUTHORIZED;
      case HTTP_STATUS.FORBIDDEN:
        return ERROR_MESSAGES.FORBIDDEN;
      case HTTP_STATUS.NOT_FOUND:
        return ERROR_MESSAGES.NOT_FOUND;
      case HTTP_STATUS.INTERNAL_SERVER_ERROR:
        return ERROR_MESSAGES.SERVER_ERROR;
      default:
        return data.detail || data.message || 'Une erreur est survenue';
    }
  } else if (error.request) {
    return ERROR_MESSAGES.NETWORK_ERROR;
  } else {
    return error.message || 'Une erreur inattendue est survenue';
  }
};

// Services API
export const authService = {
  login: async (credentials) => {
    const response = await api.post(API_ENDPOINTS.AUTH.LOGIN, credentials);
    return response;
  },

  refreshToken: async (refreshToken) => {
    const response = await refreshApi.post(API_ENDPOINTS.AUTH.REFRESH, {
      refresh: refreshToken
    });
    return response;
  },

  logout: async () => {
    try {
      // Optionnel : appeler endpoint de logout côté serveur
      await api.post(API_ENDPOINTS.AUTH.LOGOUT);
    } catch (error) {
      // Ignorer les erreurs de logout côté serveur
      console.warn('Logout server error:', error);
    } finally {
      tokenManager.clearAll();
    }
  }
};

// Services pour les différents modules
export const detectionService = {
  getDetections: async (params) => {
    return api.get(API_ENDPOINTS.DETECTIONS.BASE, { params });
  },

  getDetection: async (id) => {
    return api.get(`${API_ENDPOINTS.DETECTIONS.BASE}/${id}/`);
  },

  validateDetection: async (id) => {
    return api.post(API_ENDPOINTS.DETECTIONS.VALIDATE.replace('{id}', id));
  },

  invalidateDetection: async (id) => {
    return api.post(API_ENDPOINTS.DETECTIONS.INVALIDATE.replace('{id}', id));
  }
};

export const alertService = {
  getAlerts: async (params) => {
    return api.get(API_ENDPOINTS.ALERTS.BASE, { params });
  },

  getAlert: async (id) => {
    return api.get(`${API_ENDPOINTS.ALERTS.BASE}/${id}/`);
  },

  markAsRead: async (id) => {
    return api.post(API_ENDPOINTS.ALERTS.MARK_READ.replace('{id}', id));
  },

  assignAlert: async (id, agentId) => {
    return api.post(API_ENDPOINTS.ALERTS.ASSIGN.replace('{id}', id), {
      agent_id: agentId
    });
  }
};

export const investigationService = {
  getInvestigations: async (params) => {
    return api.get(API_ENDPOINTS.INVESTIGATIONS.BASE, { params });
  },

  getInvestigation: async (id) => {
    return api.get(`${API_ENDPOINTS.INVESTIGATIONS.BASE}/${id}/`);
  },

  createInvestigation: async (data) => {
    return api.post(API_ENDPOINTS.INVESTIGATIONS.BASE, data);
  },

  updateInvestigation: async (id, data) => {
    return api.patch(`${API_ENDPOINTS.INVESTIGATIONS.BASE}/${id}/`, data);
  },

  assignInvestigation: async (id, agentId) => {
    return api.post(API_ENDPOINTS.INVESTIGATIONS.ASSIGN.replace('{id}', id), {
      agent_id: agentId
    });
  },

  completeInvestigation: async (id, report) => {
    return api.post(API_ENDPOINTS.INVESTIGATIONS.COMPLETE.replace('{id}', id), {
      report
    });
  },

  getAvailableAgents: async () => {
    return api.get(API_ENDPOINTS.INVESTIGATIONS.AVAILABLE_AGENTS);
  }
};

export const financialRiskService = {
  getFinancialRisks: async (params) => {
    return api.get(API_ENDPOINTS.FINANCIAL_RISKS.BASE, { params });
  },

  getHighImpactRisks: async () => {
    return api.get(API_ENDPOINTS.FINANCIAL_RISKS.HIGH_IMPACT);
  }
};

export const imageService = {
  getImages: async (params) => {
    return api.get(API_ENDPOINTS.IMAGES.BASE, { params });
  },

  getImage: async (id) => {
    return api.get(`${API_ENDPOINTS.IMAGES.BASE}/${id}/`);
  },

  uploadImage: async (formData) => {
    return api.post(API_ENDPOINTS.IMAGES.UPLOAD, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  processImage: async (id) => {
    return api.post(API_ENDPOINTS.IMAGES.PROCESS.replace('{id}', id));
  }
};

export const analysisService = {
  runAnalysis: async (data) => {
    return api.post(API_ENDPOINTS.ANALYSIS.RUN, data);
  },

  getAnalysisResults: async (params) => {
    return api.get(API_ENDPOINTS.ANALYSIS.BASE, { params });
  }
};

export const statsService = {
  getDashboardStats: async () => {
    return api.get(API_ENDPOINTS.STATS.DASHBOARD);
  },

  getDetectionTrends: async (params) => {
    return api.get(API_ENDPOINTS.STATS.DETECTION_TRENDS, { params });
  },

  getRegionalStats: async (region) => {
    return api.get(API_ENDPOINTS.STATS.REGIONAL_STATS, {
      params: { region }
    });
  },

  getUserActivity: async (params) => {
    return api.get(API_ENDPOINTS.STATS.USER_ACTIVITY, { params });
  }
};

export const feedbackService = {
  getFeedbacks: async (params) => {
    return api.get(API_ENDPOINTS.FEEDBACKS.BASE, { params });
  },

  createFeedback: async (data) => {
    return api.post(API_ENDPOINTS.FEEDBACKS.BASE, data);
  }
};

export const eventService = {
  getEvents: async (params) => {
    return api.get(API_ENDPOINTS.EVENTS.BASE, { params });
  }
};

// Export de l'instance API pour usage direct si nécessaire
export default api;
