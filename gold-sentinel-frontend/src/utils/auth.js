import { USER_PERMISSIONS } from '../constants/userTypes';

// Fonction pour nettoyer le localStorage corrompu
export const cleanupLocalStorage = () => {
  const keysToCheck = ['token', 'refreshToken', 'user'];
  keysToCheck.forEach(key => {
    const value = localStorage.getItem(key);
    if (value === 'undefined' || value === 'null') {
      localStorage.removeItem(key);
    }
  });
};

// Gestion du token JWT
export const tokenManager = {
  getToken: () => {
    const token = localStorage.getItem('token');
    return (!token || token === 'undefined' || token === 'null') ? null : token;
  },
  setToken: (token) => localStorage.setItem('token', token),
  removeToken: () => localStorage.removeItem('token'),

  getRefreshToken: () => {
    const token = localStorage.getItem('refreshToken');
    return (!token || token === 'undefined' || token === 'null') ? null : token;
  },
  setRefreshToken: (token) => localStorage.setItem('refreshToken', token),
  removeRefreshToken: () => localStorage.removeItem('refreshToken'),

  clearAll: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
  }
};

// Gestion des données utilisateur
export const userManager = {
  getUser: () => {
    try {
      const user = localStorage.getItem('user');
      if (!user || user === 'undefined' || user === 'null') {
        return null;
      }
      return JSON.parse(user);
    } catch (error) {
      console.warn('Erreur lors du parsing des données utilisateur:', error);
      localStorage.removeItem('user'); // Nettoyer les données corrompues
      return null;
    }
  },
  setUser: (user) => localStorage.setItem('user', JSON.stringify(user)),
  removeUser: () => localStorage.removeItem('user'),

  getUserType: () => {
    const user = userManager.getUser();
    // Nouvelle structure : primary_authority ou authorities[0]
    return user?.primary_authority ||
           user?.authorities?.[0] ||
           user?.user_authorities?.[0]?.authority?.name ||
           'Administrateur';
  },

  getUserPermissions: () => {
    const userType = userManager.getUserType();
    return userType ? USER_PERMISSIONS[userType] : USER_PERMISSIONS['Administrateur'];
  },

  getUserAuthorities: () => {
    const user = userManager.getUser();
    // Retourne toutes les autorités de l'utilisateur
    return user?.authorities ||
           user?.user_authorities?.map(ua => ua.authority.name) ||
           ['Administrateur'];
  },

  hasAuthority: (authority) => {
    const authorities = userManager.getUserAuthorities();
    return authorities.includes(authority);
  }
};

// Vérification des permissions
export const hasPermission = (permission) => {
  const permissions = userManager.getUserPermissions();
  return permissions ? permissions[permission] : false;
};

// Vérification d'accès aux routes
export const canAccessRoute = (route) => {
  const permissions = userManager.getUserPermissions();
  return permissions ? permissions.routes.includes(route) : false;
};

// Vérification si l'utilisateur est connecté
export const isAuthenticated = () => {
  const token = tokenManager.getToken();
  const user = userManager.getUser();
  return !!(token && user);
};

// Décodage du JWT (simple, sans vérification de signature)
export const decodeToken = (token) => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    return null;
  }
};

// Vérification si le token est expiré
export const isTokenExpired = (token) => {
  const decoded = decodeToken(token);
  if (!decoded) return true;

  const currentTime = Date.now() / 1000;
  return decoded.exp < currentTime;
};

// Logout complet
export const logout = () => {
  tokenManager.clearAll();
  userManager.removeUser();
  window.location.href = '/login';
};
