{"name": "gold-sentinel-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-query": "^5.0.0", "axios": "^1.9.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.0", "leaflet": "^1.9.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.1", "react-leaflet": "^4.2.1", "react-pdf": "^7.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.1", "recharts": "^2.8.0", "xlsx": "^0.18.5", "yup": "^1.3.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}