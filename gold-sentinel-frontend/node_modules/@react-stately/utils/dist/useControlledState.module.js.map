{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAMM,SAAS,0CAA6B,KAAQ,EAAE,YAAe,EAAE,QAAyC;IAC/G,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,eAAO,EAAE,SAAS;IAEpD,IAAI,kBAAkB,CAAA,GAAA,aAAK,EAAE,UAAU;IACvC,IAAI,eAAe,UAAU;IAC7B,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,gBAAgB,gBAAgB,OAAO;QAC3C,IAAI,kBAAkB,gBAAgB,QAAQ,GAAG,CAAC,QAAQ,KAAK,cAC7D,QAAQ,IAAI,CAAC,CAAC,+BAA+B,EAAE,gBAAgB,eAAe,eAAe,IAAI,EAAE,eAAe,eAAe,eAAe,CAAC,CAAC;QAEpJ,gBAAgB,OAAO,GAAG;IAC5B,GAAG;QAAC;KAAa;IAEjB,IAAI,eAAe,eAAe,QAAQ;IAC1C,IAAI,WAAW,CAAA,GAAA,kBAAU,EAAE,CAAC,OAAO,GAAG;QACpC,IAAI,iBAAiB,CAAC,OAAO,GAAG;YAC9B,IAAI,UACF;gBAAA,IAAI,CAAC,OAAO,EAAE,CAAC,cAAc,QAC3B,SAAS,UAAU;YACrB;YAEF,IAAI,CAAC,cACH,kEAAkE;YAClE,gFAAgF;YAChF,8EAA8E;YAC9E,yFAAyF;YACzF,uDAAuD;YACvD,eAAe;QAEnB;QAEA,IAAI,OAAO,UAAU,YAAY;YAC/B,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,cAC3B,QAAQ,IAAI,CAAC;YAEf,oGAAoG;YACpG,yEAAyE;YACzE,kIAAkI;YAClI,iKAAiK;YACjK,yIAAyI;YACzI,IAAI,iBAAiB,CAAC,UAAU,GAAG;gBACjC,IAAI,mBAAmB,MAAM,eAAe,eAAe,aAAa;gBACxE,eAAe,qBAAqB;gBACpC,IAAI,CAAC,cACH,OAAO;gBAET,OAAO;YACT;YACA,cAAc;QAChB,OAAO;YACL,IAAI,CAAC,cACH,cAAc;YAEhB,eAAe,UAAU;QAC3B;IACF,GAAG;QAAC;QAAc;QAAc;KAAS;IAEzC,OAAO;QAAC;QAAc;KAAS;AACjC", "sources": ["packages/@react-stately/utils/src/useControlledState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef, useState} from 'react';\n\nexport function useControlledState<T, C = T>(value: Exclude<T, undefined>, defaultValue: Exclude<T, undefined> | undefined, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void];\nexport function useControlledState<T, C = T>(value: Exclude<T, undefined> | undefined, defaultValue: Exclude<T, undefined>, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void];\nexport function useControlledState<T, C = T>(value: T, defaultValue: T, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void] {\n  let [stateValue, setStateValue] = useState(value || defaultValue);\n\n  let isControlledRef = useRef(value !== undefined);\n  let isControlled = value !== undefined;\n  useEffect(() => {\n    let wasControlled = isControlledRef.current;\n    if (wasControlled !== isControlled && process.env.NODE_ENV !== 'production') {\n      console.warn(`WARN: A component changed from ${wasControlled ? 'controlled' : 'uncontrolled'} to ${isControlled ? 'controlled' : 'uncontrolled'}.`);\n    }\n    isControlledRef.current = isControlled;\n  }, [isControlled]);\n\n  let currentValue = isControlled ? value : stateValue;\n  let setValue = useCallback((value, ...args) => {\n    let onChangeCaller = (value, ...onChangeArgs) => {\n      if (onChange) {\n        if (!Object.is(currentValue, value)) {\n          onChange(value, ...onChangeArgs);\n        }\n      }\n      if (!isControlled) {\n        // If uncontrolled, mutate the currentValue local variable so that\n        // calling setState multiple times with the same value only emits onChange once.\n        // We do not use a ref for this because we specifically _do_ want the value to\n        // reset every render, and assigning to a ref in render breaks aborted suspended renders.\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        currentValue = value;\n      }\n    };\n\n    if (typeof value === 'function') {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn('We can not support a function callback. See Github Issues for details https://github.com/adobe/react-spectrum/issues/2320');\n      }\n      // this supports functional updates https://reactjs.org/docs/hooks-reference.html#functional-updates\n      // when someone using useControlledState calls setControlledState(myFunc)\n      // this will call our useState setState with a function as well which invokes myFunc and calls onChange with the value from myFunc\n      // if we're in an uncontrolled state, then we also return the value of myFunc which to setState looks as though it was just called with myFunc from the beginning\n      // otherwise we just return the controlled value, which won't cause a rerender because React knows to bail out when the value is the same\n      let updateFunction = (oldValue, ...functionArgs) => {\n        let interceptedValue = value(isControlled ? currentValue : oldValue, ...functionArgs);\n        onChangeCaller(interceptedValue, ...args);\n        if (!isControlled) {\n          return interceptedValue;\n        }\n        return oldValue;\n      };\n      setStateValue(updateFunction);\n    } else {\n      if (!isControlled) {\n        setStateValue(value);\n      }\n      onChangeCaller(value, ...args);\n    }\n  }, [isControlled, currentValue, onChange]);\n\n  return [currentValue, setValue];\n}\n"], "names": [], "version": 3, "file": "useControlledState.module.js.map"}