{"mappings": "AAAA;;;;;;;;;;CAUC,GAED,2EAA2E;AACpE,MAAM,4CAAoB;AAC1B,MAAM,4CAAc", "sources": ["packages/@react-aria/utils/src/constants.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Custom event names for updating the autocomplete's aria-activedecendant.\nexport const CLEAR_FOCUS_EVENT = 'react-aria-clear-focus';\nexport const FOCUS_EVENT = 'react-aria-focus';\n"], "names": [], "version": 3, "file": "constants.module.js.map"}