{"mappings": ";;;;;;;;AAAO,MAAM,4CAAmB,CAAC;QACxB;IAAP,OAAO,CAAA,oBAAA,eAAA,yBAAA,GAAI,aAAa,cAAjB,+BAAA,oBAAqB;AAC9B;AAEO,MAAM,4CAAiB,CAC5B;IAEA,IAAI,MAAM,YAAY,MAAM,GAAG,MAAM,KAAK,IACxC,OAAO;IAGT,MAAM,MAAM,0CAAiB;IAC7B,OAAO,IAAI,WAAW,IAAI;AAC5B;AAEA;;CAEC,GACD,SAAS,6BAAO,KAAc;IAC5B,OAAO,UAAU,QACf,OAAO,UAAU,YACjB,cAAc,SACd,OAAO,AAAC,MAAe,QAAQ,KAAK;AACxC;AAKO,SAAS,0CAAa,IAAiB;IAC5C,OAAO,6BAAO,SACZ,KAAK,QAAQ,KAAK,KAAK,sBAAsB,IAC7C,UAAU;AACd", "sources": ["packages/@react-aria/utils/src/domHelpers.ts"], "sourcesContent": ["export const getOwnerDocument = (el: Element | null | undefined): Document => {\n  return el?.ownerDocument ?? document;\n};\n\nexport const getOwnerWindow = (\n  el: (Window & typeof global) | Element | null | undefined\n): Window & typeof global => {\n  if (el && 'window' in el && el.window === el) {\n    return el;\n  }\n\n  const doc = getOwnerDocument(el as Element | null | undefined);\n  return doc.defaultView || window;\n};\n\n/**\n * Type guard that checks if a value is a Node. Verifies the presence and type of the nodeType property.\n */\nfunction isNode(value: unknown): value is Node {\n  return value !== null &&\n    typeof value === 'object' &&\n    'nodeType' in value &&\n    typeof (value as Node).nodeType === 'number';\n}\n/**\n * Type guard that checks if a node is a ShadowRoot. Uses nodeType and host property checks to\n * distinguish ShadowRoot from other DocumentFragments.\n */\nexport function isShadowRoot(node: Node | null): node is ShadowRoot {\n  return isNode(node) &&\n    node.nodeType === Node.DOCUMENT_FRAGMENT_NODE &&\n    'host' in node;\n}\n"], "names": [], "version": 3, "file": "domHelpers.main.js.map"}