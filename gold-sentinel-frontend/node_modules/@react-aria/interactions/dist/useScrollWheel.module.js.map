{"mappings": ";;;AAAA;;;;;;;;;;CAUC;;AAYM,SAAS,yCAAe,KAAuB,EAAE,GAAkC;IACxF,IAAI,YAAC,QAAQ,cAAE,UAAU,EAAC,GAAG;IAC7B,IAAI,kBAAkB,CAAA,GAAA,kBAAU,EAAE,CAAC;QACjC,+DAA+D;QAC/D,IAAI,EAAE,OAAO,EACX;QAGF,0BAA0B;QAC1B,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,IAAI,UACF,SAAS;YAAC,QAAQ,EAAE,MAAM;YAAE,QAAQ,EAAE,MAAM;QAAA;IAEhD,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,eAAO,EAAE,KAAK,SAAS,aAAa,YAAY;AAClD", "sources": ["packages/@react-aria/interactions/src/useScrollWheel.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject, ScrollEvents} from '@react-types/shared';\nimport {useCallback} from 'react';\nimport {useEvent} from '@react-aria/utils';\n\nexport interface ScrollWheelProps extends ScrollEvents {\n  /** Whether the scroll listener should be disabled. */\n  isDisabled?: boolean\n}\n\n// scroll wheel needs to be added not passively so it's cancelable, small helper hook to remember that\nexport function useScrollWheel(props: ScrollWheelProps, ref: RefObject<HTMLElement | null>): void {\n  let {onScroll, isDisabled} = props;\n  let onScrollHandler = useCallback((e) => {\n    // If the ctrlKey is pressed, this is a zoom event, do nothing.\n    if (e.ctrlKey) {\n      return;\n    }\n\n    // stop scrolling the page\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (onScroll) {\n      onScroll({deltaX: e.deltaX, deltaY: e.deltaY});\n    }\n  }, [onScroll]);\n\n  useEvent(ref, 'wheel', isDisabled ? undefined : onScrollHandler);\n}\n"], "names": [], "version": 3, "file": "useScrollWheel.module.js.map"}