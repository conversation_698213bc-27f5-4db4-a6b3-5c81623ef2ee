{"mappings": ";;;AAAA;;;;;;;;;;CAUC;;AAeM,SAAS,0CAAY,OAAyB;IACnD,6EAA6E;IAC7E,4EAA4E;IAC5E,2EAA2E;IAC3E,+EAA+E;IAC/E,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,uBAAe,EAAE;IACvC,MAAM,gBAAgB,CAAA,GAAA,uBAAe,EAAE;IACvC,IAAI,CAAA,GAAA,yCAAqB,QAAQ,WAAW;QAC1C,IAAI,qBAAqB;QACzB,CAAA,GAAA,yBAAiB,EAAE;YACjB,4EAA4E;YAC5E,IAAI,CAAA,GAAA,uBAAe,EAAE,mBAAmB,sBAAsB,QAAQ,WAAW,EAC/E,CAAA,GAAA,4BAAoB,EAAE;QAE1B;IACF,OACE,CAAA,GAAA,4BAAoB,EAAE;AAE1B", "sources": ["packages/@react-aria/interactions/src/focusSafely.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the 'License');\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an 'AS IS' BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {\n  focusWithoutScrolling,\n  getActiveElement,\n  getOwnerDocument,\n  runAfterTransition\n} from '@react-aria/utils';\nimport {getInteractionModality} from './useFocusVisible';\n\n/**\n * A utility function that focuses an element while avoiding undesired side effects such\n * as page scrolling and screen reader issues with CSS transitions.\n */\nexport function focusSafely(element: FocusableElement): void {\n  // If the user is interacting with a virtual cursor, e.g. screen reader, then\n  // wait until after any animated transitions that are currently occurring on\n  // the page before shifting focus. This avoids issues with VoiceOver on iOS\n  // causing the page to scroll when moving focus if the element is transitioning\n  // from off the screen.\n  const ownerDocument = getOwnerDocument(element);\n  const activeElement = getActiveElement(ownerDocument);\n  if (getInteractionModality() === 'virtual') {\n    let lastFocusedElement = activeElement;\n    runAfterTransition(() => {\n      // If focus did not move and the element is still in the document, focus it.\n      if (getActiveElement(ownerDocument) === lastFocusedElement && element.isConnected) {\n        focusWithoutScrolling(element);\n      }\n    });\n  } else {\n    focusWithoutScrolling(element);\n  }\n}\n"], "names": [], "version": 3, "file": "focusSafely.module.js.map"}