{"mappings": ";;;;AAAA;;;;;;;;;;CAUC;;;AAYM,MAAM,0DAAiB,CAAA,GAAA,YAAI,EAAE,UAAU,CAAC,CAAC,YAAC,QAAQ,EAAE,GAAG,OAA2B,EAAE;IACzF,IAAI,eAAe,CAAA,GAAA,aAAK,EAAE;IAC1B,IAAI,cAAc,CAAA,GAAA,iBAAS,EAAE,CAAA,GAAA,yCAAoB;IACjD,MAAM,CAAA,GAAA,mBAAW,EAAE,QAAO,wBAAA,kCAAA,YAAa,GAAG;IAC1C,IAAI,UAAU,CAAA,GAAA,iBAAS,EAAE,eAAe,CAAC,GAAG;QAC1C,GAAG,KAAK;aACR;QACA;YACE,aAAa,OAAO,GAAG;YACvB,IAAI,aACF,YAAY,QAAQ;QAExB;IACF;IAEA,CAAA,GAAA,iBAAS,EAAE,aAAa;IAExB,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,cAC3B,QAAQ,IAAI,CACV;YAIJ,aAAa,OAAO,GAAG,MAAM,iCAAiC;QAChE;IACF,GAAG,EAAE;IAEL,qBACE,gCAAC,CAAA,GAAA,yCAAoB,EAAE,QAAQ;QAAC,OAAO;OACpC;AAGP;AAEO,SAAS,0CAAoB,YAAC,QAAQ,EAAwB;IACnE,IAAI,UAAU,CAAA,GAAA,cAAM,EAAE,IAAO,CAAA;YAAC,UAAU,KAAO;QAAC,CAAA,GAAI,EAAE;IACtD,qBACE,gCAAC,CAAA,GAAA,yCAAoB,EAAE,QAAQ;QAAC,OAAO;OACpC;AAGP", "sources": ["packages/@react-aria/interactions/src/PressResponder.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {mergeProps, useObjectRef, useSyncRef} from '@react-aria/utils';\nimport {PressProps} from './usePress';\nimport {PressResponderContext} from './context';\nimport React, {ForwardedRef, JSX, ReactNode, useContext, useEffect, useMemo, useRef} from 'react';\n\ninterface PressResponderProps extends PressProps {\n  children: ReactNode\n}\n\nexport const PressResponder = React.forwardRef(({children, ...props}: PressResponderProps, ref: ForwardedRef<FocusableElement>) => {\n  let isRegistered = useRef(false);\n  let prevContext = useContext(PressResponderContext);\n  ref = useObjectRef(ref || prevContext?.ref);\n  let context = mergeProps(prevContext || {}, {\n    ...props,\n    ref,\n    register() {\n      isRegistered.current = true;\n      if (prevContext) {\n        prevContext.register();\n      }\n    }\n  });\n\n  useSyncRef(prevContext, ref);\n\n  useEffect(() => {\n    if (!isRegistered.current) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\n          'A PressResponder was rendered without a pressable child. ' +\n          'Either call the usePress hook, or wrap your DOM node with <Pressable> component.'\n        );\n      }\n      isRegistered.current = true; // only warn once in strict mode.\n    }\n  }, []);\n\n  return (\n    <PressResponderContext.Provider value={context}>\n      {children}\n    </PressResponderContext.Provider>\n  );\n});\n\nexport function ClearPressResponder({children}: {children: ReactNode}): JSX.Element {\n  let context = useMemo(() => ({register: () => {}}), []);\n  return (\n    <PressResponderContext.Provider value={context}>\n      {children}\n    </PressResponderContext.Provider>\n  );\n}\n"], "names": [], "version": 3, "file": "PressResponder.module.js.map"}