{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAmBD,0GAA0G;AAC1G,4HAA4H;AAC5H,IAAI,8BAAe;AACnB,IAAI,wCAAkB;AACtB,IAAI,2CAAqB,IAAI;AAEtB,SAAS,0CAAqB,MAAgB;IACnD,IAAI,CAAA,GAAA,YAAI,KAAK;QACX,IAAI,gCAAU,WAAW;YAEvB,MAAM,iBAAiB,CAAA,GAAA,uBAAe,EAAE;YACxC,wCAAkB,eAAe,eAAe,CAAC,KAAK,CAAC,gBAAgB;YACvE,eAAe,eAAe,CAAC,KAAK,CAAC,gBAAgB,GAAG;QAC1D;QAEA,8BAAQ;IACV,OAAO,IAAI,kBAAkB,eAAe,kBAAkB,YAAY;QACxE,sFAAsF;QACtF,kDAAkD;QAClD,IAAI,WAAW,gBAAgB,OAAO,KAAK,GAAG,eAAe;QAC7D,yCAAmB,GAAG,CAAC,QAAQ,OAAO,KAAK,CAAC,SAAS;QACrD,OAAO,KAAK,CAAC,SAAS,GAAG;IAC3B;AACF;AAEO,SAAS,0CAAqB,MAAgB;IACnD,IAAI,CAAA,GAAA,YAAI,KAAK;QACX,0DAA0D;QAC1D,sEAAsE;QACtE,IAAI,gCAAU,YACZ;QAGF,8BAAQ;QAER,uEAAuE;QACvE,+DAA+D;QAC/D,WAAW;YACT,uEAAuE;YACvE,oEAAoE;YACpE,CAAA,GAAA,yBAAiB,EAAE;gBACjB,wBAAwB;gBACxB,IAAI,gCAAU,aAAa;oBAEzB,MAAM,iBAAiB,CAAA,GAAA,uBAAe,EAAE;oBACxC,IAAI,eAAe,eAAe,CAAC,KAAK,CAAC,gBAAgB,KAAK,QAC5D,eAAe,eAAe,CAAC,KAAK,CAAC,gBAAgB,GAAG,yCAAmB;oBAG7E,wCAAkB;oBAClB,8BAAQ;gBACV;YACF;QACF,GAAG;IACL,OAAO,IAAI,kBAAkB,eAAe,kBAAkB,YAC5D,+DAA+D;IAC/D,kDAAkD;IAClD;QAAA,IAAI,UAAU,yCAAmB,GAAG,CAAC,SAAS;YAC5C,IAAI,sBAAsB,yCAAmB,GAAG,CAAC;YACjD,IAAI,WAAW,gBAAgB,OAAO,KAAK,GAAG,eAAe;YAE7D,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,QAC7B,OAAO,KAAK,CAAC,SAAS,GAAG;YAG3B,IAAI,OAAO,YAAY,CAAC,aAAa,IACnC,OAAO,eAAe,CAAC;YAEzB,yCAAmB,MAAM,CAAC;QAC5B;IAAA;AAEJ", "sources": ["packages/@react-aria/interactions/src/textSelection.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getOwnerDocument, isIOS, runAfterTransition} from '@react-aria/utils';\n\n// Safari on iOS starts selecting text on long press. The only way to avoid this, it seems,\n// is to add user-select: none to the entire page. Adding it to the pressable element prevents\n// that element from being selected, but nearby elements may still receive selection. We add\n// user-select: none on touch start, and remove it again on touch end to prevent this.\n// This must be implemented using global state to avoid race conditions between multiple elements.\n\n// There are three possible states due to the delay before removing user-select: none after\n// pointer up. The 'default' state always transitions to the 'disabled' state, which transitions\n// to 'restoring'. The 'restoring' state can either transition back to 'disabled' or 'default'.\n\n// For non-iOS devices, we apply user-select: none to the pressed element instead to avoid possible\n// performance issues that arise from applying and removing user-select: none to the entire page\n// (see https://github.com/adobe/react-spectrum/issues/1609).\ntype State = 'default' | 'disabled' | 'restoring';\n\n// Note that state only matters here for iOS. Non-iOS gets user-select: none applied to the target element\n// rather than at the document level so we just need to apply/remove user-select: none for each pressed element individually\nlet state: State = 'default';\nlet savedUserSelect = '';\nlet modifiedElementMap = new WeakMap<Element, string>();\n\nexport function disableTextSelection(target?: Element): void {\n  if (isIOS()) {\n    if (state === 'default') {\n\n      const documentObject = getOwnerDocument(target);\n      savedUserSelect = documentObject.documentElement.style.webkitUserSelect;\n      documentObject.documentElement.style.webkitUserSelect = 'none';\n    }\n\n    state = 'disabled';\n  } else if (target instanceof HTMLElement || target instanceof SVGElement) {\n    // If not iOS, store the target's original user-select and change to user-select: none\n    // Ignore state since it doesn't apply for non iOS\n    let property = 'userSelect' in target.style ? 'userSelect' : 'webkitUserSelect';\n    modifiedElementMap.set(target, target.style[property]);\n    target.style[property] = 'none';\n  }\n}\n\nexport function restoreTextSelection(target?: Element): void {\n  if (isIOS()) {\n    // If the state is already default, there's nothing to do.\n    // If it is restoring, then there's no need to queue a second restore.\n    if (state !== 'disabled') {\n      return;\n    }\n\n    state = 'restoring';\n\n    // There appears to be a delay on iOS where selection still might occur\n    // after pointer up, so wait a bit before removing user-select.\n    setTimeout(() => {\n      // Wait for any CSS transitions to complete so we don't recompute style\n      // for the whole page in the middle of the animation and cause jank.\n      runAfterTransition(() => {\n        // Avoid race conditions\n        if (state === 'restoring') {\n\n          const documentObject = getOwnerDocument(target);\n          if (documentObject.documentElement.style.webkitUserSelect === 'none') {\n            documentObject.documentElement.style.webkitUserSelect = savedUserSelect || '';\n          }\n\n          savedUserSelect = '';\n          state = 'default';\n        }\n      });\n    }, 300);\n  } else if (target instanceof HTMLElement || target instanceof SVGElement) {\n    // If not iOS, restore the target's original user-select if any\n    // Ignore state since it doesn't apply for non iOS\n    if (target && modifiedElementMap.has(target)) {\n      let targetOldUserSelect = modifiedElementMap.get(target) as string;\n      let property = 'userSelect' in target.style ? 'userSelect' : 'webkitUserSelect';\n\n      if (target.style[property] === 'none') {\n        target.style[property] = targetOldUserSelect;\n      }\n\n      if (target.getAttribute('style') === '') {\n        target.removeAttribute('style');\n      }\n      modifiedElementMap.delete(target);\n    }\n  }\n}\n"], "names": [], "version": 3, "file": "textSelection.module.js.map"}