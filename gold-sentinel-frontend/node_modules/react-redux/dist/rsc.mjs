// src/utils/shallowEqual.ts
function is(x, y) {
  if (x === y) {
    return x !== 0 || y !== 0 || 1 / x === 1 / y;
  } else {
    return x !== x && y !== y;
  }
}
function shallowEqual(objA, objB) {
  if (is(objA, objB)) return true;
  if (typeof objA !== "object" || objA === null || typeof objB !== "object" || objB === null) {
    return false;
  }
  const keysA = Object.keys(objA);
  const keysB = Object.keys(objB);
  if (keysA.length !== keysB.length) return false;
  for (let i = 0; i < keysA.length; i++) {
    if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {
      return false;
    }
  }
  return true;
}

// src/index-rsc.ts
var throwNotSupportedError = (...args) => {
  throw new Error(
    "This function is not supported in React Server Components. Please only use this export in a Client Component."
  );
};
var ReactReduxContext = {};
export {
  throwNotSupportedError as Provider,
  ReactReduxContext,
  throwNotSupportedError as batch,
  throwNotSupportedError as connect,
  throwNotSupportedError as createDispatchHook,
  throwNotSupportedError as createSelectorHook,
  throwNotSupportedError as createStoreHook,
  shallowEqual,
  throwNotSupportedError as useDispatch,
  throwNotSupportedError as useSelector,
  throwNotSupportedError as useStore
};
//# sourceMappingURL=rsc.mjs.map